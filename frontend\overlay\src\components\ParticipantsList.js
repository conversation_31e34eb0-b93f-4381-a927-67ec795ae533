import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Users, User, Clock } from 'lucide-react';

const ParticipantsList = ({ participants, theme, maxHeight = "60vh" }) => {
  const [visibleParticipants, setVisibleParticipants] = useState([]);
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    // تحديث المشاركين المرئيين
    if (showAll || participants.length <= 10) {
      setVisibleParticipants(participants);
    } else {
      setVisibleParticipants(participants.slice(0, 10));
    }
  }, [participants, showAll]);

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 100 }}
      animate={{ opacity: 1, x: 0 }}
      className="glass-effect rounded-xl p-4 min-w-[280px]"
      style={{
        borderColor: theme.secondary_color,
        maxHeight: maxHeight,
        overflow: 'hidden'
      }}
    >
      {/* العنوان */}
      <div className="flex items-center gap-2 mb-3">
        <Users size={20} className="text-white" />
        <h3 className="text-lg font-bold text-white">المشاركين</h3>
        <span className="bg-white bg-opacity-20 rounded-full px-2 py-1 text-xs text-white font-bold">
          {participants.length}
        </span>
      </div>

      {/* قائمة المشاركين */}
      <div className="space-y-2 max-h-[calc(60vh-80px)] overflow-y-auto">
        <AnimatePresence>
          {visibleParticipants.map((participant, index) => (
            <motion.div
              key={participant.user_id}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ delay: index * 0.1 }}
              className="list-item flex items-center justify-between p-2 rounded-lg bg-white bg-opacity-10 hover:bg-opacity-20 transition-all"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center text-white font-bold text-sm">
                  {participant.username.charAt(0).toUpperCase()}
                </div>
                <div>
                  <p className="text-white font-medium text-sm">
                    {participant.username}
                  </p>
                  <div className="flex items-center gap-1 text-xs text-gray-300">
                    <Clock size={10} />
                    <span>{formatTime(participant.joined_at)}</span>
                  </div>
                </div>
              </div>
              
              {/* تأثير الانضمام */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="w-2 h-2 rounded-full bg-green-400 animate-pulse"
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* زر إظهار المزيد */}
      {participants.length > 10 && (
        <motion.button
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          onClick={() => setShowAll(!showAll)}
          className="w-full mt-3 py-2 text-sm text-white bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-all button-hover"
        >
          {showAll ? 'إظهار أقل' : `إظهار المزيد (${participants.length - 10})`}
        </motion.button>
      )}

      {/* رسالة عند عدم وجود مشاركين */}
      {participants.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-8"
        >
          <User size={32} className="mx-auto mb-2 text-gray-400" />
          <p className="text-gray-400 text-sm">لا يوجد مشاركين بعد</p>
        </motion.div>
      )}

      {/* تأثيرات بصرية */}
      <motion.div
        animate={{ 
          boxShadow: [
            `0 0 10px ${theme.secondary_color}20`,
            `0 0 20px ${theme.secondary_color}40`,
            `0 0 10px ${theme.secondary_color}20`
          ]
        }}
        transition={{ duration: 3, repeat: Infinity }}
        className="absolute inset-0 rounded-xl pointer-events-none"
      />
    </motion.div>
  );
};

export default ParticipantsList; 