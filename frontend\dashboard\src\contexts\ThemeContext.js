import React, { createContext, useContext, useState, useEffect } from 'react';
import themeService from '../services/themeService';
import toast from 'react-hot-toast';

/**
 * Theme Context للتحكم في السمات عبر التطبيق
 */
const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState(null);
  const [themes, setThemes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // تحميل السمة الحالية عند بدء التطبيق
  useEffect(() => {
    loadCurrentTheme();
    loadAllThemes();
  }, []);

  // تطبيق السمة على DOM عند تغييرها
  useEffect(() => {
    if (currentTheme) {
      themeService.applyThemeToDOM(currentTheme);
    }
  }, [currentTheme]);

  /**
   * تحميل السمة الحالية
   */
  const loadCurrentTheme = async () => {
    try {
      const theme = await themeService.getCurrentTheme();
      setCurrentTheme(theme);
    } catch (error) {
      console.error('خطأ في تحميل السمة الحالية:', error);
      // استخدام سمة افتراضية في حالة الخطأ
      setCurrentTheme({
        primary_color: '#FF6B6B',
        secondary_color: '#4ECDC4',
        font: 'Cairo',
        icon_set: 'default'
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * تحميل جميع السمات
   */
  const loadAllThemes = async () => {
    try {
      const allThemes = await themeService.getAllThemes();
      setThemes(allThemes);
    } catch (error) {
      console.error('خطأ في تحميل السمات:', error);
    }
  };

  /**
   * تطبيق سمة جديدة
   */
  const applyTheme = async (theme) => {
    try {
      await themeService.applyTheme(theme);
      setCurrentTheme(theme);
      toast.success('تم تطبيق السمة بنجاح');
      return true;
    } catch (error) {
      toast.error(error.message || 'فشل في تطبيق السمة');
      return false;
    }
  };

  /**
   * إنشاء سمة جديدة
   */
  const createTheme = async (themeData) => {
    try {
      const result = await themeService.createTheme(themeData);
      await loadAllThemes(); // إعادة تحميل السمات
      toast.success('تم إنشاء السمة بنجاح');
      return result.theme;
    } catch (error) {
      toast.error(error.message || 'فشل في إنشاء السمة');
      throw error;
    }
  };

  /**
   * حذف سمة
   */
  const deleteTheme = async (themeId) => {
    try {
      await themeService.deleteTheme(themeId);
      await loadAllThemes(); // إعادة تحميل السمات
      toast.success('تم حذف السمة بنجاح');
      return true;
    } catch (error) {
      toast.error(error.message || 'فشل في حذف السمة');
      return false;
    }
  };

  /**
   * معاينة سمة مؤقتة
   */
  const previewTheme = (theme) => {
    return themeService.previewTheme(theme);
  };

  /**
   * تصدير السمات
   */
  const exportThemes = async () => {
    try {
      await themeService.exportThemes();
      toast.success('تم تصدير السمات بنجاح');
      return true;
    } catch (error) {
      toast.error('فشل في تصدير السمات');
      return false;
    }
  };

  /**
   * استيراد السمات
   */
  const importThemes = async (file) => {
    try {
      const importedThemes = await themeService.importThemes(file);
      await loadAllThemes(); // إعادة تحميل السمات
      toast.success(`تم استيراد ${importedThemes.length} سمة بنجاح`);
      return importedThemes;
    } catch (error) {
      toast.error(error.message || 'فشل في استيراد السمات');
      throw error;
    }
  };

  /**
   * إنشاء السمات الافتراضية
   */
  const createDefaultThemes = async () => {
    try {
      const createdThemes = await themeService.createDefaultThemes();
      await loadAllThemes(); // إعادة تحميل السمات
      toast.success(`تم إنشاء ${createdThemes.length} سمة افتراضية`);
      return createdThemes;
    } catch (error) {
      toast.error('فشل في إنشاء السمات الافتراضية');
      throw error;
    }
  };

  /**
   * الحصول على لون بناءً على نوعه
   */
  const getThemeColor = (type = 'primary') => {
    if (!currentTheme) return '#FF6B6B';
    
    switch (type) {
      case 'primary':
        return currentTheme.primary_color;
      case 'secondary':
        return currentTheme.secondary_color;
      default:
        return currentTheme.primary_color;
    }
  };

  /**
   * الحصول على الخط الحالي
   */
  const getCurrentFont = () => {
    return currentTheme?.font || 'Cairo';
  };

  /**
   * الحصول على مجموعة الأيقونات الحالية
   */
  const getCurrentIconSet = () => {
    return currentTheme?.icon_set || 'default';
  };

  /**
   * التحقق من كون السمة نشطة
   */
  const isThemeActive = (theme) => {
    if (!currentTheme || !theme) return false;
    
    return (
      currentTheme.primary_color === theme.primary_color &&
      currentTheme.secondary_color === theme.secondary_color &&
      currentTheme.font === theme.font &&
      currentTheme.icon_set === theme.icon_set
    );
  };

  const value = {
    // State
    currentTheme,
    themes,
    isLoading,

    // Actions
    applyTheme,
    createTheme,
    deleteTheme,
    previewTheme,
    exportThemes,
    importThemes,
    createDefaultThemes,
    loadCurrentTheme,
    loadAllThemes,

    // Utilities
    getThemeColor,
    getCurrentFont,
    getCurrentIconSet,
    isThemeActive
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext;
