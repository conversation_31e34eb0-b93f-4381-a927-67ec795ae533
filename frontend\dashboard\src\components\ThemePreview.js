import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Play, 
  Users, 
  Trophy, 
  Star,
  Heart,
  Zap,
  Crown,
  Gift
} from 'lucide-react';

/**
 * مكون معاينة السمة
 */
const ThemePreview = ({ theme, isActive = false, onApply, className = '' }) => {
  const [isHovered, setIsHovered] = useState(false);

  if (!theme) return null;

  const previewStyle = {
    '--preview-primary': theme.primary_color,
    '--preview-secondary': theme.secondary_color,
    fontFamily: theme.font
  };

  return (
    <motion.div
      className={`relative overflow-hidden rounded-xl border-2 transition-all duration-300 ${
        isActive 
          ? 'border-green-500 shadow-lg shadow-green-500/20' 
          : 'border-gray-200 hover:border-gray-300'
      } ${className}`}
      style={previewStyle}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      {/* Theme Preview Header */}
      <div 
        className="h-24 relative flex items-center justify-center text-white"
        style={{ 
          background: `linear-gradient(135deg, ${theme.primary_color}, ${theme.secondary_color})` 
        }}
      >
        <div className="text-center">
          <h3 className="text-lg font-bold mb-1">{theme.name}</h3>
          <p className="text-sm opacity-90">معاينة السمة</p>
        </div>
        
        {/* Active Badge */}
        {isActive && (
          <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            نشط
          </div>
        )}
      </div>

      {/* Preview Content */}
      <div className="p-4 bg-white">
        {/* Sample UI Elements */}
        <div className="space-y-3">
          {/* Sample Buttons */}
          <div className="flex gap-2">
            <button 
              className="px-3 py-1 rounded text-white text-sm font-medium transition-colors"
              style={{ backgroundColor: theme.primary_color }}
            >
              <Play className="w-3 h-3 inline mr-1" />
              بدء اللعبة
            </button>
            <button 
              className="px-3 py-1 rounded text-white text-sm font-medium transition-colors"
              style={{ backgroundColor: theme.secondary_color }}
            >
              <Users className="w-3 h-3 inline mr-1" />
              المشاركون
            </button>
          </div>

          {/* Sample Stats */}
          <div className="grid grid-cols-2 gap-2">
            <div className="p-2 rounded-lg bg-gray-50 text-center">
              <Trophy className="w-4 h-4 mx-auto mb-1" style={{ color: theme.primary_color }} />
              <div className="text-xs font-medium">الفائزون</div>
              <div className="text-sm font-bold">24</div>
            </div>
            <div className="p-2 rounded-lg bg-gray-50 text-center">
              <Star className="w-4 h-4 mx-auto mb-1" style={{ color: theme.secondary_color }} />
              <div className="text-xs font-medium">النقاط</div>
              <div className="text-sm font-bold">1,250</div>
            </div>
          </div>

          {/* Sample Progress Bar */}
          <div className="space-y-1">
            <div className="text-xs text-gray-600">تقدم الجلسة</div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="h-2 rounded-full transition-all duration-300"
                style={{ 
                  background: `linear-gradient(90deg, ${theme.primary_color}, ${theme.secondary_color})`,
                  width: '65%'
                }}
              ></div>
            </div>
          </div>

          {/* Sample Icons based on icon set */}
          <div className="flex justify-center gap-2 pt-2">
            {theme.icon_set === 'gaming' && (
              <>
                <Crown className="w-4 h-4" style={{ color: theme.primary_color }} />
                <Gift className="w-4 h-4" style={{ color: theme.secondary_color }} />
                <Zap className="w-4 h-4" style={{ color: theme.primary_color }} />
              </>
            )}
            {theme.icon_set === 'modern' && (
              <>
                <Star className="w-4 h-4" style={{ color: theme.primary_color }} />
                <Heart className="w-4 h-4" style={{ color: theme.secondary_color }} />
                <Trophy className="w-4 h-4" style={{ color: theme.primary_color }} />
              </>
            )}
            {theme.icon_set === 'neon' && (
              <>
                <Zap className="w-4 h-4 animate-pulse" style={{ color: theme.primary_color }} />
                <Star className="w-4 h-4 animate-pulse" style={{ color: theme.secondary_color }} />
                <Crown className="w-4 h-4 animate-pulse" style={{ color: theme.primary_color }} />
              </>
            )}
            {theme.icon_set === 'default' && (
              <>
                <Play className="w-4 h-4" style={{ color: theme.primary_color }} />
                <Users className="w-4 h-4" style={{ color: theme.secondary_color }} />
                <Trophy className="w-4 h-4" style={{ color: theme.primary_color }} />
              </>
            )}
          </div>
        </div>

        {/* Theme Details */}
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>الخط: {theme.font}</span>
            <span>الأيقونات: {theme.icon_set}</span>
          </div>
          <div className="flex items-center gap-1 mt-1">
            <div 
              className="w-3 h-3 rounded-full border border-gray-300"
              style={{ backgroundColor: theme.primary_color }}
              title={theme.primary_color}
            ></div>
            <div 
              className="w-3 h-3 rounded-full border border-gray-300"
              style={{ backgroundColor: theme.secondary_color }}
              title={theme.secondary_color}
            ></div>
            <span className="text-xs text-gray-400 mr-1">الألوان</span>
          </div>
        </div>
      </div>

      {/* Hover Overlay */}
      {isHovered && onApply && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"
        >
          <button
            onClick={() => onApply(theme)}
            className="px-4 py-2 bg-white text-gray-900 rounded-lg font-medium hover:bg-gray-100 transition-colors"
          >
            تطبيق السمة
          </button>
        </motion.div>
      )}
    </motion.div>
  );
};

/**
 * مكون معاينة مصغرة للسمة
 */
export const ThemePreviewMini = ({ theme, isActive = false, onClick, className = '' }) => {
  if (!theme) return null;

  return (
    <motion.div
      className={`relative w-16 h-16 rounded-lg overflow-hidden cursor-pointer border-2 transition-all ${
        isActive 
          ? 'border-green-500 shadow-lg shadow-green-500/20' 
          : 'border-gray-200 hover:border-gray-300'
      } ${className}`}
      onClick={() => onClick && onClick(theme)}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <div 
        className="w-full h-full"
        style={{ 
          background: `linear-gradient(135deg, ${theme.primary_color}, ${theme.secondary_color})` 
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
          <div className="text-white text-xs font-medium text-center">
            {theme.name.substring(0, 6)}
          </div>
        </div>
      </div>
      
      {isActive && (
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
          <div className="w-2 h-2 bg-white rounded-full"></div>
        </div>
      )}
    </motion.div>
  );
};

/**
 * مكون معاينة السمة في الـ Overlay
 */
export const OverlayThemePreview = ({ theme, className = '' }) => {
  if (!theme) return null;

  return (
    <div 
      className={`p-6 rounded-xl text-white relative overflow-hidden ${className}`}
      style={{ 
        background: `linear-gradient(135deg, ${theme.primary_color}, ${theme.secondary_color})`,
        fontFamily: theme.font
      }}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-16 -translate-y-16"></div>
        <div className="absolute bottom-0 right-0 w-24 h-24 bg-white rounded-full translate-x-12 translate-y-12"></div>
      </div>

      <div className="relative z-10">
        <h2 className="text-2xl font-bold mb-2">جلسة تجريبية</h2>
        <p className="text-lg opacity-90 mb-4">معاينة مظهر الـ Overlay</p>
        
        <div className="flex items-center gap-4 mb-4">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            <span>12 مشارك</span>
          </div>
          <div className="flex items-center gap-2">
            <Trophy className="w-5 h-5" />
            <span>3 فائزين</span>
          </div>
        </div>

        <div className="bg-white bg-opacity-20 rounded-lg p-3 backdrop-blur-sm">
          <div className="text-sm opacity-90 mb-1">الفائز الحالي</div>
          <div className="text-lg font-bold">اللاعب التجريبي</div>
        </div>
      </div>
    </div>
  );
};

export default ThemePreview;
