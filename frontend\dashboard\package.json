{"name": "casino-dashboard", "version": "1.0.0", "description": "Admin Dashboard for Casino Session Manager", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.4.0", "recharts": "^2.7.2", "framer-motion": "^10.16.4", "lucide-react": "^0.292.0", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15"}}