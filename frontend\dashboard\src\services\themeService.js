/**
 * خدمة إدارة السمات (Themes)
 */

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class ThemeService {
  /**
   * جلب جميع السمات
   */
  async getAllThemes() {
    try {
      const response = await fetch(`${API_URL}/api/themes`);
      if (!response.ok) {
        throw new Error('فشل في جلب السمات');
      }
      const data = await response.json();
      return data.themes || [];
    } catch (error) {
      console.error('خطأ في جلب السمات:', error);
      throw error;
    }
  }

  /**
   * جلب السمة الحالية
   */
  async getCurrentTheme() {
    try {
      const response = await fetch(`${API_URL}/api/themes/current`);
      if (!response.ok) {
        throw new Error('فشل في جلب السمة الحالية');
      }
      return await response.json();
    } catch (error) {
      console.error('خطأ في جلب السمة الحالية:', error);
      throw error;
    }
  }

  /**
   * إنشاء سمة جديدة
   */
  async createTheme(themeData) {
    try {
      const response = await fetch(`${API_URL}/api/themes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(themeData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'فشل في إنشاء السمة');
      }

      return await response.json();
    } catch (error) {
      console.error('خطأ في إنشاء السمة:', error);
      throw error;
    }
  }

  /**
   * تطبيق سمة
   */
  async applyTheme(themeData) {
    try {
      const response = await fetch(`${API_URL}/api/themes/apply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(themeData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'فشل في تطبيق السمة');
      }

      const result = await response.json();
      
      // تطبيق السمة على الواجهة
      this.applyThemeToDOM(result.theme);
      
      return result;
    } catch (error) {
      console.error('خطأ في تطبيق السمة:', error);
      throw error;
    }
  }

  /**
   * حذف سمة
   */
  async deleteTheme(themeId) {
    try {
      const response = await fetch(`${API_URL}/api/themes/${themeId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'فشل في حذف السمة');
      }

      return await response.json();
    } catch (error) {
      console.error('خطأ في حذف السمة:', error);
      throw error;
    }
  }

  /**
   * تطبيق السمة على DOM
   */
  applyThemeToDOM(theme) {
    if (!theme) return;

    // تطبيق المتغيرات CSS
    const root = document.documentElement;
    root.style.setProperty('--primary-color', theme.primary_color);
    root.style.setProperty('--secondary-color', theme.secondary_color);
    
    // تطبيق الخط
    document.body.style.fontFamily = theme.font;
    
    // إضافة class للـ icon set
    document.body.className = document.body.className.replace(/icon-set-\w+/g, '');
    document.body.classList.add(`icon-set-${theme.icon_set}`);
  }

  /**
   * تصدير السمات
   */
  async exportThemes() {
    try {
      const themes = await this.getAllThemes();
      const dataStr = JSON.stringify(themes, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `themes-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      
      return true;
    } catch (error) {
      console.error('خطأ في تصدير السمات:', error);
      throw error;
    }
  }

  /**
   * استيراد السمات
   */
  async importThemes(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        try {
          const themes = JSON.parse(e.target.result);
          
          if (!Array.isArray(themes)) {
            throw new Error('تنسيق الملف غير صحيح');
          }

          const importedThemes = [];
          
          for (const theme of themes) {
            try {
              // إزالة الـ id و created_at لتجنب التضارب
              const { id, created_at, ...themeData } = theme;
              
              // إضافة بادئة للاسم لتجنب التضارب
              themeData.name = `مستورد-${themeData.name}`;
              
              const result = await this.createTheme(themeData);
              importedThemes.push(result.theme);
            } catch (error) {
              console.warn(`فشل في استيراد السمة ${theme.name}:`, error);
            }
          }
          
          resolve(importedThemes);
        } catch (error) {
          reject(new Error('فشل في قراءة الملف'));
        }
      };
      
      reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
      reader.readAsText(file);
    });
  }

  /**
   * إنشاء سمات افتراضية
   */
  async createDefaultThemes() {
    const defaultThemes = [
      {
        name: 'الليل الداكن',
        primary_color: '#1a1a2e',
        secondary_color: '#16213e',
        font: 'Cairo',
        icon_set: 'modern'
      },
      {
        name: 'الغروب الذهبي',
        primary_color: '#ff6b35',
        secondary_color: '#f7931e',
        font: 'Cairo',
        icon_set: 'default'
      },
      {
        name: 'البحر الأزرق',
        primary_color: '#0077be',
        secondary_color: '#00a8cc',
        font: 'Cairo',
        icon_set: 'modern'
      },
      {
        name: 'الغابة الخضراء',
        primary_color: '#2d5016',
        secondary_color: '#3e7b27',
        font: 'Cairo',
        icon_set: 'default'
      },
      {
        name: 'النيون الوردي',
        primary_color: '#ff0080',
        secondary_color: '#ff4081',
        font: 'Cairo',
        icon_set: 'neon'
      }
    ];

    const createdThemes = [];
    
    for (const theme of defaultThemes) {
      try {
        const result = await this.createTheme(theme);
        createdThemes.push(result.theme);
      } catch (error) {
        console.warn(`فشل في إنشاء السمة الافتراضية ${theme.name}:`, error);
      }
    }
    
    return createdThemes;
  }

  /**
   * معاينة السمة بدون تطبيقها
   */
  previewTheme(theme, element = document.body) {
    const originalStyles = {
      primaryColor: element.style.getPropertyValue('--primary-color'),
      secondaryColor: element.style.getPropertyValue('--secondary-color'),
      fontFamily: element.style.fontFamily,
      className: element.className
    };

    // تطبيق المعاينة
    element.style.setProperty('--primary-color', theme.primary_color);
    element.style.setProperty('--secondary-color', theme.secondary_color);
    element.style.fontFamily = theme.font;
    element.className = element.className.replace(/icon-set-\w+/g, '');
    element.classList.add(`icon-set-${theme.icon_set}`);

    // إرجاع دالة لإلغاء المعاينة
    return () => {
      element.style.setProperty('--primary-color', originalStyles.primaryColor);
      element.style.setProperty('--secondary-color', originalStyles.secondaryColor);
      element.style.fontFamily = originalStyles.fontFamily;
      element.className = originalStyles.className;
    };
  }
}

// إنشاء instance واحد من الخدمة
const themeService = new ThemeService();

export default themeService;
