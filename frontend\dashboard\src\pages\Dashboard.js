import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

import {
  Users,
  Trophy,
  Gamepad2,
  TrendingUp,
  Calendar,
  Clock,
  Star
} from 'lucide-react';

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalSessions: 0,
    totalUsers: 0,
    totalWinners: 0,
    activeSessions: 0
  });
  const [recentSessions, setRecentSessions] = useState([]);
  const [topPlayers, setTopPlayers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // محاكاة جلب البيانات من API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // بيانات تجريبية
      setStats({
        totalSessions: 156,
        totalUsers: 1247,
        totalWinners: 89,
        activeSessions: 2
      });

      setRecentSessions([
        {
          id: 1,
          name: 'جلسة سلوتس مسائية',
          gameType: 'slots',
          status: 'open',
          participants: 23,
          createdAt: '2024-01-15T18:30:00'
        },
        {
          id: 2,
          name: 'روليت السبت',
          gameType: 'roulette',
          status: 'closed',
          participants: 45,
          createdAt: '2024-01-15T16:00:00'
        },
        {
          id: 3,
          name: 'عجلة الحظ الأسبوعية',
          gameType: 'wheel',
          status: 'ended',
          participants: 67,
          createdAt: '2024-01-14T20:00:00'
        }
      ]);

      setTopPlayers([
        {
          id: 1,
          username: 'أحمد محمد',
          totalWins: 15,
          totalPoints: 450,
          winRate: 68.2
        },
        {
          id: 2,
          username: 'فاطمة علي',
          totalWins: 12,
          totalPoints: 380,
          winRate: 54.5
        },
        {
          id: 3,
          username: 'محمد أحمد',
          totalWins: 10,
          totalPoints: 320,
          winRate: 45.5
        }
      ]);
    } catch (error) {
      console.error('خطأ في جلب بيانات لوحة التحكم:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getGameIcon = (gameType) => {
    switch (gameType) {
      case 'slots':
        return '🎰';
      case 'roulette':
        return '🎲';
      case 'wheel':
        return '🎡';
      default:
        return '🎮';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open':
        return 'bg-success text-white';
      case 'closed':
        return 'bg-warning text-dark';
      case 'ended':
        return 'bg-danger text-white';
      default:
        return 'bg-secondary text-white';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'open':
        return 'مفتوحة';
      case 'closed':
        return 'مغلقة';
      case 'ended':
        return 'منتهية';
      default:
        return 'غير معروف';
    }
  };

  if (isLoading) {
    return (
      <div className="d-flex align-items-center justify-content-center" style={{height: '16rem'}}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="mb-4">
        <h1 className="display-5 fw-bold text-dark mb-2">لوحة التحكم</h1>
        <p className="text-muted">مرحباً بك في نظام إدارة جلسات الكازينو</p>
      </div>

      {/* Stats Cards */}
      <div className="row g-4 mb-4">
        <div className="col-md-6 col-lg-3">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="card h-100 border-0 shadow-sm"
          >
            <div className="card-body">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <p className="small fw-medium text-muted mb-1">إجمالي الجلسات</p>
                  <h3 className="h2 fw-bold text-dark mb-0">{stats.totalSessions}</h3>
                </div>
                <div className="bg-primary bg-opacity-10 rounded p-3">
                  <Gamepad2 className="text-primary" size={24} />
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        <div className="col-md-6 col-lg-3">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="card h-100 border-0 shadow-sm"
          >
            <div className="card-body">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <p className="small fw-medium text-muted mb-1">إجمالي المستخدمين</p>
                  <h3 className="h2 fw-bold text-dark mb-0">{stats.totalUsers}</h3>
                </div>
                <div className="bg-success bg-opacity-10 rounded p-3">
                  <Users className="text-success" size={24} />
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        <div className="col-md-6 col-lg-3">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="card h-100 border-0 shadow-sm"
          >
            <div className="card-body">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <p className="small fw-medium text-muted mb-1">إجمالي الفائزين</p>
                  <h3 className="h2 fw-bold text-dark mb-0">{stats.totalWinners}</h3>
                </div>
                <div className="bg-warning bg-opacity-10 rounded p-3">
                  <Trophy className="text-warning" size={24} />
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        <div className="col-md-6 col-lg-3">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="card h-100 border-0 shadow-sm"
          >
            <div className="card-body">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <p className="small fw-medium text-muted mb-1">الجلسات النشطة</p>
                  <h3 className="h2 fw-bold text-dark mb-0">{stats.activeSessions}</h3>
                </div>
                <div className="bg-info bg-opacity-10 rounded p-3">
                  <TrendingUp className="text-info" size={24} />
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Content Grid */}
      <div className="row g-4">
        {/* Recent Sessions */}
        <div className="col-lg-6">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            className="card border-0 shadow-sm h-100"
          >
            <div className="card-body">
              <div className="d-flex align-items-center justify-content-between mb-4">
                <h2 className="h4 fw-bold text-dark mb-0">الجلسات الأخيرة</h2>
                <Calendar className="text-muted" size={20} />
              </div>

              <div className="d-grid gap-3">
                {recentSessions.map((session) => (
                  <div key={session.id} className="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                    <div className="d-flex align-items-center">
                      <div className="fs-4 me-3">{getGameIcon(session.gameType)}</div>
                      <div>
                        <p className="fw-medium text-dark mb-1">{session.name}</p>
                        <div className="d-flex align-items-center text-muted small">
                          <Clock size={16} className="me-1" />
                          <span>{new Date(session.createdAt).toLocaleString('ar-SA')}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-end">
                      <span className={`badge rounded-pill ${getStatusColor(session.status)}`}>
                        {getStatusText(session.status)}
                      </span>
                      <p className="small text-muted mt-1 mb-0">{session.participants} مشارك</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Top Players */}
        <div className="col-lg-6">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6 }}
            className="card border-0 shadow-sm h-100"
          >
            <div className="card-body">
              <div className="d-flex align-items-center justify-content-between mb-4">
                <h2 className="h4 fw-bold text-dark mb-0">أفضل اللاعبين</h2>
                <Star className="text-muted" size={20} />
              </div>

              <div className="d-grid gap-3">
                {topPlayers.map((player, index) => (
                  <div key={player.id} className="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                    <div className="d-flex align-items-center">
                      <div className="bg-gradient text-white rounded-circle d-flex align-items-center justify-content-center fw-bold me-3" style={{width: '32px', height: '32px', fontSize: '14px'}}>
                        {index + 1}
                      </div>
                      <div>
                        <p className="fw-medium text-dark mb-1">{player.username}</p>
                        <p className="small text-muted mb-0">{player.totalWins} فوز</p>
                      </div>
                    </div>
                    <div className="text-end">
                      <p className="fw-bold text-dark mb-1">{player.totalPoints} نقطة</p>
                      <p className="small text-muted mb-0">{player.winRate}% نسبة فوز</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;