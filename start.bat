@echo off
echo ========================================
echo    نظام إدارة جلسات الكازينو
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

REM التحقق من وجود Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Node.js غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

echo ✅ تم التحقق من المتطلبات الأساسية
echo.

REM إنشاء البيئة الافتراضية إذا لم تكن موجودة
if not exist "venv" (
    echo 📦 إنشاء البيئة الافتراضية...
    python -m venv venv
)

REM تفعيل البيئة الافتراضية
echo 🔄 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

REM تثبيت متطلبات Python
echo 📥 تثبيت متطلبات Python...
pip install -r requirements.txt

REM إنشاء مجلد قاعدة البيانات
if not exist "database" mkdir database

echo.
echo 🚀 بدء تشغيل النظام...
echo.

REM تشغيل الباك إند
start "Backend" cmd /k "venv\Scripts\activate.bat && python main.py"

REM انتظار قليل
timeout /t 3 /nobreak >nul

REM تشغيل الـOverlay
if exist "frontend\overlay" (
    echo 🎥 تشغيل الـOverlay...
    start "Overlay" cmd /k "cd frontend\overlay && npm install && npm start"
)

REM تشغيل لوحة التحكم
if exist "frontend\dashboard" (
    echo 🛠️ تشغيل لوحة التحكم...
    start "Dashboard" cmd /k "cd frontend\dashboard && npm install && npm start"
)

echo.
echo ✅ تم تشغيل جميع الخدمات!
echo.
echo 🌐 الروابط المتاحة:
echo    الباك إند API: http://localhost:8000
echo    لوحة التحكم: http://localhost:3000
echo    الـOverlay: http://localhost:3001
echo.
echo 📖 للمزيد من المعلومات، راجع docs/SETUP.md
echo.
pause 