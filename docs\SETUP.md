# دليل الإعداد والتشغيل

## المتطلبات الأساسية

- Python 3.8+
- Node.js 16+
- Git
- Discord Bot Token
- Discord OAuth2 Credentials

## خطوات الإعداد

### 1. إعداد Discord Bot

1. اذه<PERSON> إلى [Discord Developer Portal](https://discord.com/developers/applications)
2. أنشئ تطبيق جديد
3. اذه<PERSON> إلى قسم "Bot" وأنشئ بوت
4. انسخ Token البوت
5. اذهب إلى قسم "OAuth2" وانسخ Client ID و Client Secret
6. أضف الصلاحيات التالية للبوت:
   - Send Messages
   - Use Slash Commands
   - Manage Guild
   - Read Message History

### 2. إعداد المشروع

```bash
# استنساخ المشروع
git clone <repository-url>
cd ISlotcall

# إنشاء البيئة الافتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows

# تثبيت متطلبات Python
pip install -r requirements.txt
```

### 3. إعداد متغيرات البيئة

```bash
# نسخ ملف البيئة
cp .env.example .env

# تعديل الملف بالمعلومات المطلوبة
nano .env
```

أضف المعلومات التالية:

```env
# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
DISCORD_CLIENT_ID=your_discord_client_id_here
DISCORD_CLIENT_SECRET=your_discord_client_secret_here
DISCORD_GUILD_ID=your_guild_id_here

# Database Configuration
DATABASE_URL=sqlite:///./database/casino.db

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
SECRET_KEY=your_secret_key_here

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
OVERLAY_URL=http://localhost:3001

# OAuth2 Configuration
REDIRECT_URI=http://localhost:8000/auth/callback

# Admin Role Configuration
ADMIN_ROLE_NAME=Streamer Admin
```

### 4. إعداد الفرونت إند

```bash
# إعداد الـOverlay
cd frontend/overlay
npm install

# إعداد لوحة التحكم
cd ../dashboard
npm install
```

### 5. تشغيل النظام

#### الطريقة الأولى: تشغيل يدوي

```bash
# تشغيل الباك إند
python main.py

# في terminal منفصل - تشغيل الـOverlay
cd frontend/overlay
npm start

# في terminal منفصل - تشغيل لوحة التحكم
cd frontend/dashboard
npm start
```

#### الطريقة الثانية: استخدام Docker

```bash
# بناء وتشغيل جميع الخدمات
docker-compose up --build

# أو تشغيل في الخلفية
docker-compose up -d --build
```

## الوصول للنظام

- **الباك إند API**: http://localhost:8000
- **لوحة التحكم**: http://localhost:3000
- **الـOverlay**: http://localhost:3001

## إعداد OBS

1. افتح OBS Studio
2. أضف مصدر جديد من نوع "Browser"
3. أدخل الرابط: `http://localhost:3001`
4. اضبط الأبعاد حسب الحاجة
5. تأكد من تفعيل "Refresh browser when scene becomes active"

## إعداد Discord

1. اذهب إلى سيرفر Discord
2. أضف البوت باستخدام رابط OAuth2
3. أنشئ رول "Streamer Admin" أو استخدم رول موجود
4. تأكد من أن البوت لديه الصلاحيات المطلوبة

## الأوامر المتاحة

### أوامر الأدمن
- `/start_slot` - بدء جلسة جديدة
- `/reset_slot` - إعادة تعيين جميع الجلسات
- `/slot_off` - إغلاق التسجيل
- `/pick_winner` - اختيار فائز
- `/set_theme` - تخصيص المظهر
- `/save_theme` - حفظ Theme
- `/load_theme` - تحميل Theme

### أوامر المستخدم
- `/slot` - الانضمام/الانسحاب من جلسة
- `/my_stats` - عرض الإحصائيات الشخصية

## استكشاف الأخطاء

### مشاكل شائعة

1. **البوت لا يستجيب للأوامر**
   - تأكد من صحة Token البوت
   - تأكد من إضافة البوت للسيرفر
   - تأكد من الصلاحيات

2. **الـOverlay لا يعرض البيانات**
   - تأكد من تشغيل الباك إند
   - تحقق من اتصال Socket.IO
   - تأكد من صحة URL في OBS

3. **خطأ في قاعدة البيانات**
   - تأكد من وجود مجلد database
   - تحقق من صلاحيات الكتابة
   - أعد تشغيل الباك إند

### سجلات النظام

```bash
# عرض سجلات الباك إند
tail -f logs/backend.log

# عرض سجلات Docker
docker-compose logs -f backend
```

## التحديثات

```bash
# تحديث الكود
git pull origin main

# إعادة بناء Docker
docker-compose down
docker-compose up --build -d
```

## النسخ الاحتياطي

```bash
# نسخ قاعدة البيانات
cp database/casino.db backup/casino_$(date +%Y%m%d_%H%M%S).db

# استعادة قاعدة البيانات
cp backup/casino_backup.db database/casino.db
```

## الدعم

للمساعدة والدعم التقني، يرجى التواصل عبر:
- GitHub Issues
- Discord Server
- Email Support 