import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

import { 
  Users, 
  Trophy, 
  Gamepad2, 
  TrendingUp,
  Calendar,
  Clock,
  Star
} from 'lucide-react';

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalSessions: 0,
    totalUsers: 0,
    totalWinners: 0,
    activeSessions: 0
  });
  const [recentSessions, setRecentSessions] = useState([]);
  const [topPlayers, setTopPlayers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // محاكاة جلب البيانات من API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // بيانات تجريبية
      setStats({
        totalSessions: 156,
        totalUsers: 1247,
        totalWinners: 89,
        activeSessions: 2
      });

      setRecentSessions([
        {
          id: 1,
          name: 'جلسة سلوتس مسائية',
          gameType: 'slots',
          status: 'open',
          participants: 23,
          createdAt: '2024-01-15T18:30:00'
        },
        {
          id: 2,
          name: 'روليت السبت',
          gameType: 'roulette',
          status: 'closed',
          participants: 45,
          createdAt: '2024-01-15T16:00:00'
        },
        {
          id: 3,
          name: 'عجلة الحظ الأسبوعية',
          gameType: 'wheel',
          status: 'ended',
          participants: 67,
          createdAt: '2024-01-14T20:00:00'
        }
      ]);

      setTopPlayers([
        {
          id: 1,
          username: 'أحمد محمد',
          totalWins: 15,
          totalPoints: 450,
          winRate: 68.2
        },
        {
          id: 2,
          username: 'فاطمة علي',
          totalWins: 12,
          totalPoints: 380,
          winRate: 54.5
        },
        {
          id: 3,
          username: 'محمد أحمد',
          totalWins: 10,
          totalPoints: 320,
          winRate: 45.5
        }
      ]);
    } catch (error) {
      console.error('خطأ في جلب بيانات لوحة التحكم:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getGameIcon = (gameType) => {
    switch (gameType) {
      case 'slots':
        return '🎰';
      case 'roulette':
        return '🎲';
      case 'wheel':
        return '🎡';
      default:
        return '🎮';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open':
        return 'text-green-600 bg-green-100';
      case 'closed':
        return 'text-yellow-600 bg-yellow-100';
      case 'ended':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'open':
        return 'مفتوحة';
      case 'closed':
        return 'مغلقة';
      case 'ended':
        return 'منتهية';
      default:
        return 'غير معروف';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-800 mb-2">لوحة التحكم</h1>
        <p className="text-gray-600">مرحباً بك في نظام إدارة جلسات الكازينو</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="stat-card"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي الجلسات</p>
              <p className="text-2xl font-bold text-gray-800">{stats.totalSessions}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Gamepad2 className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="stat-card"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
              <p className="text-2xl font-bold text-gray-800">{stats.totalUsers}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="stat-card"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي الفائزين</p>
              <p className="text-2xl font-bold text-gray-800">{stats.totalWinners}</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Trophy className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="stat-card"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">الجلسات النشطة</p>
              <p className="text-2xl font-bold text-gray-800">{stats.activeSessions}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Sessions */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-800">الجلسات الأخيرة</h2>
            <Calendar className="w-5 h-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {recentSessions.map((session) => (
              <div key={session.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="text-2xl">{getGameIcon(session.gameType)}</div>
                  <div>
                    <p className="font-medium text-gray-800">{session.name}</p>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <Clock className="w-4 h-4" />
                      <span>{new Date(session.createdAt).toLocaleString('ar-SA')}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                    {getStatusText(session.status)}
                  </span>
                  <p className="text-sm text-gray-600 mt-1">{session.participants} مشارك</p>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Top Players */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-800">أفضل اللاعبين</h2>
            <Star className="w-5 h-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {topPlayers.map((player, index) => (
              <div key={player.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-gray-800">{player.username}</p>
                    <p className="text-sm text-gray-500">{player.totalWins} فوز</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-gray-800">{player.totalPoints} نقطة</p>
                  <p className="text-sm text-gray-500">{player.winRate}% نسبة فوز</p>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard; 