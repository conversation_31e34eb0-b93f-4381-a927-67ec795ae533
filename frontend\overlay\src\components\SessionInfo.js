import React from 'react';
import { motion } from 'framer-motion';
import { Gamepad2, Clock, Users, Target } from 'lucide-react';

const SessionInfo = ({ session, theme }) => {
  const getGameIcon = (gameType) => {
    switch (gameType) {
      case 'slots':
        return '🎰';
      case 'roulette':
        return '🎲';
      case 'wheel':
        return '🎡';
      default:
        return '🎮';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open':
        return 'bg-green-500';
      case 'closed':
        return 'bg-yellow-500';
      case 'ended':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'open':
        return 'مفتوحة';
      case 'closed':
        return 'مغلقة';
      case 'ended':
        return 'منتهية';
      default:
        return 'غير معروف';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="glass-effect rounded-xl p-4 min-w-[300px]"
      style={{
        borderColor: theme.primary_color,
        boxShadow: `0 0 20px ${theme.primary_color}40`
      }}
    >
      {/* عنوان الجلسة */}
      <div className="flex items-center gap-3 mb-3">
        <div className="text-3xl">{getGameIcon(session.game_type)}</div>
        <div>
          <h2 className="text-xl font-bold text-white text-glow">
            {session.name}
          </h2>
          <p className="text-sm text-gray-300 capitalize">
            {session.game_type}
          </p>
        </div>
      </div>

      {/* حالة الجلسة */}
      <div className="flex items-center gap-2 mb-3">
        <div className={`w-3 h-3 rounded-full ${getStatusColor(session.status)} animate-pulse`}></div>
        <span className="text-white font-medium">
          {getStatusText(session.status)}
        </span>
      </div>

      {/* معلومات إضافية */}
      <div className="grid grid-cols-2 gap-3 text-sm">
        <div className="flex items-center gap-2 text-gray-300">
          <Clock size={14} />
          <span>
            {new Date(session.created_at).toLocaleTimeString('ar-SA', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </span>
        </div>
        
        <div className="flex items-center gap-2 text-gray-300">
          <Users size={14} />
          <span>{session.participants_count || 0} مشارك</span>
        </div>
      </div>

      {/* شريط التقدم (إذا كانت الجلسة مغلقة) */}
      {session.status === 'closed' && (
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: '100%' }}
          className="mt-3 h-1 bg-gray-600 rounded-full overflow-hidden"
        >
          <div 
            className="h-full rounded-full transition-all duration-1000"
            style={{ 
              backgroundColor: theme.secondary_color,
              width: '100%'
            }}
          ></div>
        </motion.div>
      )}

      {/* تأثيرات بصرية إضافية */}
      {session.status === 'open' && (
        <motion.div
          animate={{ 
            boxShadow: [
              `0 0 10px ${theme.primary_color}40`,
              `0 0 20px ${theme.primary_color}60`,
              `0 0 10px ${theme.primary_color}40`
            ]
          }}
          transition={{ duration: 2, repeat: Infinity }}
          className="absolute inset-0 rounded-xl pointer-events-none"
        />
      )}
    </motion.div>
  );
};

export default SessionInfo; 