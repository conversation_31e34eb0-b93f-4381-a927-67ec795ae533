import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Palette, Paintbrush, Type, Image } from 'lucide-react';

/**
 * مكون اختبار السمات - لعرض كيفية استخدام Theme Context
 */
const ThemeTestComponent = () => {
  const { 
    currentTheme, 
    getThemeColor, 
    getCurrentFont, 
    getCurrentIconSet 
  } = useTheme();

  if (!currentTheme) {
    return <div>جاري تحميل السمة...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">اختبار نظام السمات</h2>
      
      {/* Current Theme Info */}
      <div className="bg-white rounded-lg p-4 shadow-sm border">
        <h3 className="text-lg font-semibold mb-3">معلومات السمة الحالية</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              اللون الأساسي
            </label>
            <div className="flex items-center gap-2">
              <div 
                className="w-6 h-6 rounded border"
                style={{ backgroundColor: getThemeColor('primary') }}
              ></div>
              <span className="text-sm">{getThemeColor('primary')}</span>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              اللون الثانوي
            </label>
            <div className="flex items-center gap-2">
              <div 
                className="w-6 h-6 rounded border"
                style={{ backgroundColor: getThemeColor('secondary') }}
              ></div>
              <span className="text-sm">{getThemeColor('secondary')}</span>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الخط الحالي
            </label>
            <span className="text-sm">{getCurrentFont()}</span>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              مجموعة الأيقونات
            </label>
            <span className="text-sm">{getCurrentIconSet()}</span>
          </div>
        </div>
      </div>

      {/* Theme-aware Components */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">مكونات تستخدم السمة</h3>
        
        {/* Primary Color Button */}
        <button 
          className="theme-primary text-white px-4 py-2 rounded-lg hover:opacity-90 transition-opacity"
        >
          <Paintbrush className="w-4 h-4 inline mr-2" />
          زر باللون الأساسي
        </button>

        {/* Secondary Color Button */}
        <button 
          className="theme-secondary text-white px-4 py-2 rounded-lg hover:opacity-90 transition-opacity ml-3"
        >
          <Palette className="w-4 h-4 inline mr-2" />
          زر باللون الثانوي
        </button>

        {/* Gradient Background */}
        <div className="theme-gradient text-white p-4 rounded-lg">
          <h4 className="text-lg font-bold mb-2">خلفية متدرجة</h4>
          <p>هذا النص يستخدم الخلفية المتدرجة للسمة الحالية</p>
        </div>

        {/* Theme Border */}
        <div className="p-4 border-2 theme-border rounded-lg bg-white">
          <h4 className="theme-primary-text text-lg font-bold mb-2">
            <Type className="w-5 h-5 inline mr-2" />
            حدود ملونة
          </h4>
          <p className="text-gray-600">هذا المربع يستخدم حدود باللون الأساسي للسمة</p>
        </div>

        {/* Icon Set Demo */}
        <div className="bg-white p-4 rounded-lg border">
          <h4 className="text-lg font-bold mb-3">
            <Image className="w-5 h-5 inline mr-2" />
            عرض مجموعة الأيقونات: {getCurrentIconSet()}
          </h4>
          <div className="flex gap-4">
            <Palette className={`w-8 h-8 theme-icon theme-primary-text`} />
            <Paintbrush className={`w-8 h-8 theme-icon theme-secondary-text`} />
            <Type className={`w-8 h-8 theme-icon theme-primary-text`} />
            <Image className={`w-8 h-8 theme-icon theme-secondary-text`} />
          </div>
        </div>

        {/* Font Demo */}
        <div className="bg-white p-4 rounded-lg border">
          <h4 className="text-lg font-bold mb-3">عرض الخط: {getCurrentFont()}</h4>
          <div className="space-y-2" style={{ fontFamily: getCurrentFont() }}>
            <p className="text-2xl font-bold">نص كبير وعريض</p>
            <p className="text-lg font-medium">نص متوسط</p>
            <p className="text-base">نص عادي للقراءة</p>
            <p className="text-sm text-gray-600">نص صغير للتفاصيل</p>
          </div>
        </div>

        {/* Glow Effects */}
        <div className="bg-gray-900 p-4 rounded-lg">
          <h4 className="text-white text-lg font-bold mb-3">تأثيرات التوهج</h4>
          <div className="space-y-3">
            <div className="theme-glow p-3 rounded-lg bg-gray-800 text-white">
              مربع متوهج باللون الأساسي
            </div>
            <div className="theme-text-glow text-white text-xl font-bold text-center">
              نص متوهج باللون الأساسي
            </div>
          </div>
        </div>
      </div>

      {/* CSS Variables Display */}
      <div className="bg-gray-100 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">CSS Variables المطبقة</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm font-mono">
          <div>--primary-color: {getThemeColor('primary')}</div>
          <div>--secondary-color: {getThemeColor('secondary')}</div>
          <div>font-family: {getCurrentFont()}</div>
          <div>icon-set: {getCurrentIconSet()}</div>
        </div>
      </div>
    </div>
  );
};

export default ThemeTestComponent;
