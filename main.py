#!/usr/bin/env python3
"""
نظام إدارة جلسات ألعاب الكازينو - الملف الرئيسي
"""

import asyncio
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import socketio
from dotenv import load_dotenv
import os
from contextlib import asynccontextmanager

from backend.database.database import init_db
from backend.api.routes import router as api_router
from backend.bot.discord_bot import DiscordBot

# تحميل متغيرات البيئة
load_dotenv()

# متغير عام للبوت
discord_bot = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """إدارة دورة حياة التطبيق"""
    global discord_bot
    
    # Startup
    print("🚀 بدء تشغيل نظام إدارة جلسات الكازينو...")
    
    # تهيئة قاعدة البيانات
    await init_db()
    print("✅ تم تهيئة قاعدة البيانات")
    
    # بدء تشغيل بوت ديسكورد (اختياري)
    discord_token = os.getenv("DISCORD_TOKEN")
    if discord_token:
        try:
            discord_bot = DiscordBot(sio)
            await discord_bot.start(discord_token)
            print("✅ تم تشغيل بوت ديسكورد")
        except Exception as e:
            print(f"⚠️ فشل في تشغيل بوت ديسكورد: {e}")
            discord_bot = None
    else:
        print("ℹ️ لم يتم تعيين DISCORD_TOKEN - سيتم تشغيل النظام بدون بوت ديسكورد")
        discord_bot = None
    
    yield
    
    # Shutdown
    print("🛑 إغلاق النظام...")
    
    if discord_bot:
        await discord_bot.close()
        print("✅ تم إغلاق بوت ديسكورد")

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="Casino Session Manager",
    description="نظام إدارة جلسات ألعاب الكازينو",
    version="1.0.0",
    lifespan=lifespan
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        os.getenv("FRONTEND_URL", "http://localhost:3000"),
        os.getenv("OVERLAY_URL", "http://localhost:3001")
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إنشاء Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins=[
        os.getenv("FRONTEND_URL", "http://localhost:3000"),
        os.getenv("OVERLAY_URL", "http://localhost:3001")
    ]
)

# دمج Socket.IO مع FastAPI
socket_app = socketio.ASGIApp(sio, app)

# إضافة routes
app.include_router(api_router, prefix="/api")

# Socket.IO events
@sio.event
async def connect(sid, environ):
    """عند اتصال عميل جديد"""
    print(f"🔗 عميل متصل: {sid}")

@sio.event
async def disconnect(sid):
    """عند انفصال عميل"""
    print(f"🔌 عميل منفصل: {sid}")

@sio.event
async def join_overlay(sid, data):
    """انضمام عميل للـoverlay"""
    await sio.emit('user_joined_overlay', data, room=sid)

if __name__ == "__main__":
    # تشغيل الخادم
    uvicorn.run(
        "main:socket_app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", 8000)),
        reload=True,
        log_level="info"
    ) 