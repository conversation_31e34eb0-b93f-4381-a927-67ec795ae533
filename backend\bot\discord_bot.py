"""
بوت ديسكورد الرئيسي للنظام
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import random
from datetime import datetime
import os
from dotenv import load_dotenv
from sqlalchemy.orm import Session

from backend.database.database import SessionLocal, Session as DBSession, Participant, Winner, Theme, AdminLog, UserStats

load_dotenv()

class DiscordBot(commands.Bot):
    def __init__(self, sio):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.members = True
        
        super().__init__(command_prefix="!", intents=intents)
        self.sio = sio
        self.current_session = None
        
    async def setup_hook(self):
        """إعداد البوت عند بدء التشغيل"""
        await self.add_cog(SlotCommands(self))
        await self.tree.sync()
        print("✅ تم إعداد أوامر البوت")

    async def on_ready(self):
        """عند جاهزية البوت"""
        print(f"🤖 البوت متصل باسم: {self.user}")
        print(f"🆔 معرف البوت: {self.user.id}")

class SlotCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.db = SessionLocal()

    def is_admin(self, interaction: discord.Interaction) -> bool:
        """التحقق من صلاحيات الأدمن"""
        # التحقق من صلاحية إدارة السيرفر
        if interaction.user.guild_permissions.manage_guild:
            return True
        
        # التحقق من وجود رول الأدمن
        admin_role_name = os.getenv("ADMIN_ROLE_NAME", "Streamer Admin")
        admin_role = discord.utils.get(interaction.guild.roles, name=admin_role_name)
        
        if admin_role and admin_role in interaction.user.roles:
            return True
            
        return False

    def log_admin_action(self, action: str, performed_by: str, details: str = None):
        """تسجيل إجراء أدمن"""
        try:
            log = AdminLog(
                action=action,
                performed_by=performed_by,
                details=details
            )
            self.db.add(log)
            self.db.commit()
        except Exception as e:
            print(f"خطأ في تسجيل إجراء الأدمن: {e}")

    @app_commands.command(name="start_slot", description="بدء جلسة جديدة")
    @app_commands.describe(
        call_name="اسم الجلسة",
        game="نوع اللعبة"
    )
    @app_commands.choices(game=[
        app_commands.Choice(name="Slots", value="slots"),
        app_commands.Choice(name="Roulette", value="roulette"),
        app_commands.Choice(name="Wheel", value="wheel")
    ])
    async def start_slot(self, interaction: discord.Interaction, call_name: str, game: str):
        """بدء جلسة جديدة"""
        if not self.is_admin(interaction):
            await interaction.response.send_message("❌ ليس لديك صلاحية لاستخدام هذا الأمر!", ephemeral=True)
            return

        try:
            # إنشاء جلسة جديدة
            session = DBSession(
                name=call_name,
                game_type=game,
                status='open'
            )
            self.db.add(session)
            self.db.commit()
            
            # تحديث الجلسة الحالية
            self.bot.current_session = session
            
            # إرسال رسالة تأكيد
            embed = discord.Embed(
                title="🎮 جلسة جديدة مفتوحة!",
                description=f"**{call_name}** - {game.title()}",
                color=0x00ff00
            )
            embed.add_field(name="الحالة", value="✅ مفتوحة للتسجيل", inline=True)
            embed.add_field(name="النوع", value=game.title(), inline=True)
            embed.set_footer(text=f"ابدأ بواسطة {interaction.user.display_name}")
            
            await interaction.response.send_message(embed=embed)
            
            # تحديث الـoverlay
            await self.bot.sio.emit('session_started', {
                'session_id': session.id,
                'name': call_name,
                'game_type': game,
                'status': 'open'
            })
            
            # تسجيل الإجراء
            self.log_admin_action(
                f"بدء جلسة {game}",
                interaction.user.display_name,
                f"اسم الجلسة: {call_name}"
            )
            
        except Exception as e:
            await interaction.response.send_message(f"❌ خطأ في بدء الجلسة: {str(e)}", ephemeral=True)

    @app_commands.command(name="reset_slot", description="إعادة تعيين جميع الجلسات")
    async def reset_slot(self, interaction: discord.Interaction):
        """إعادة تعيين جميع الجلسات"""
        if not self.is_admin(interaction):
            await interaction.response.send_message("❌ ليس لديك صلاحية لاستخدام هذا الأمر!", ephemeral=True)
            return

        try:
            # حذف جميع الجلسات النشطة
            active_sessions = self.db.query(DBSession).filter(DBSession.status.in_(['open', 'closed'])).all()
            for session in active_sessions:
                session.status = 'ended'
                session.closed_at = datetime.utcnow()
            
            self.db.commit()
            
            # إعادة تعيين الجلسة الحالية
            self.bot.current_session = None
            
            embed = discord.Embed(
                title="🔄 إعادة تعيين النظام",
                description="تم إعادة تعيين جميع الجلسات النشطة",
                color=0xff6b6b
            )
            embed.add_field(name="الجلسات المغلقة", value=len(active_sessions), inline=True)
            
            await interaction.response.send_message(embed=embed)
            
            # تحديث الـoverlay
            await self.bot.sio.emit('sessions_reset')
            
            # تسجيل الإجراء
            self.log_admin_action(
                "إعادة تعيين جميع الجلسات",
                interaction.user.display_name,
                f"عدد الجلسات المغلقة: {len(active_sessions)}"
            )
            
        except Exception as e:
            await interaction.response.send_message(f"❌ خطأ في إعادة التعيين: {str(e)}", ephemeral=True)

    @app_commands.command(name="slot_off", description="إغلاق التسجيل للجلسة الحالية")
    async def slot_off(self, interaction: discord.Interaction):
        """إغلاق التسجيل للجلسة الحالية"""
        if not self.is_admin(interaction):
            await interaction.response.send_message("❌ ليس لديك صلاحية لاستخدام هذا الأمر!", ephemeral=True)
            return

        if not self.bot.current_session:
            await interaction.response.send_message("❌ لا توجد جلسة نشطة!", ephemeral=True)
            return

        try:
            # إغلاق الجلسة
            self.bot.current_session.status = 'closed'
            self.db.commit()
            
            embed = discord.Embed(
                title="🔒 إغلاق التسجيل",
                description=f"تم إغلاق التسجيل لجلسة **{self.bot.current_session.name}**",
                color=0xffa500
            )
            embed.add_field(name="المشاركين", value=len(self.bot.current_session.participants), inline=True)
            
            await interaction.response.send_message(embed=embed)
            
            # تحديث الـoverlay
            await self.bot.sio.emit('session_closed', {
                'session_id': self.bot.current_session.id,
                'participants_count': len(self.bot.current_session.participants)
            })
            
            # تسجيل الإجراء
            self.log_admin_action(
                "إغلاق التسجيل",
                interaction.user.display_name,
                f"جلسة: {self.bot.current_session.name}"
            )
            
        except Exception as e:
            await interaction.response.send_message(f"❌ خطأ في إغلاق الجلسة: {str(e)}", ephemeral=True)

    @app_commands.command(name="pick_winner", description="اختيار فائز عشوائي")
    async def pick_winner(self, interaction: discord.Interaction):
        """اختيار فائز عشوائي"""
        if not self.is_admin(interaction):
            await interaction.response.send_message("❌ ليس لديك صلاحية لاستخدام هذا الأمر!", ephemeral=True)
            return

        if not self.bot.current_session:
            await interaction.response.send_message("❌ لا توجد جلسة نشطة!", ephemeral=True)
            return

        participants = self.bot.current_session.participants
        if not participants:
            await interaction.response.send_message("❌ لا يوجد مشاركين في الجلسة!", ephemeral=True)
            return

        try:
            # اختيار فائز عشوائي
            winner = random.choice(participants)
            
            # إضافة الفائز لقاعدة البيانات
            winner_record = Winner(
                session_id=self.bot.current_session.id,
                user_id=winner.user_id,
                username=winner.username
            )
            self.db.add(winner_record)
            
            # تحديث إحصائيات المستخدم
            user_stats = self.db.query(UserStats).filter(UserStats.user_id == winner.user_id).first()
            if not user_stats:
                user_stats = UserStats(
                    user_id=winner.user_id,
                    username=winner.username,
                    total_participations=1,
                    total_wins=1,
                    last_win=datetime.utcnow(),
                    points=10
                )
                self.db.add(user_stats)
            else:
                user_stats.total_wins += 1
                user_stats.last_win = datetime.utcnow()
                user_stats.points += 10
            
            self.db.commit()
            
            # إنشاء رسالة الفائز
            embed = discord.Embed(
                title="🎉 مبروك للفائز!",
                description=f"**{winner.username}** فاز في جلسة **{self.bot.current_session.name}**!",
                color=0xffd700
            )
            embed.add_field(name="اللعبة", value=self.bot.current_session.game_type.title(), inline=True)
            embed.add_field(name="النقاط المكتسبة", value="10", inline=True)
            embed.set_thumbnail(url="https://cdn.discordapp.com/emojis/🎰.png")
            
            await interaction.response.send_message(embed=embed)
            
            # تحديث الـoverlay
            await self.bot.sio.emit('winner_picked', {
                'session_id': self.bot.current_session.id,
                'winner': {
                    'user_id': winner.user_id,
                    'username': winner.username
                },
                'game_type': self.bot.current_session.game_type
            })
            
            # تسجيل الإجراء
            self.log_admin_action(
                "اختيار فائز",
                interaction.user.display_name,
                f"الفائز: {winner.username} - الجلسة: {self.bot.current_session.name}"
            )
            
        except Exception as e:
            await interaction.response.send_message(f"❌ خطأ في اختيار الفائز: {str(e)}", ephemeral=True)

    @app_commands.command(name="set_theme", description="تخصيص مظهر الـoverlay")
    @app_commands.describe(
        color="اللون الأساسي (hex)",
        font="نوع الخط",
        icon_set="مجموعة الأيقونات"
    )
    async def set_theme(self, interaction: discord.Interaction, color: str, font: str = "Arial", icon_set: str = "default"):
        """تخصيص مظهر الـoverlay"""
        if not self.is_admin(interaction):
            await interaction.response.send_message("❌ ليس لديك صلاحية لاستخدام هذا الأمر!", ephemeral=True)
            return

        try:
            # التحقق من صحة اللون
            if not color.startswith('#') or len(color) != 7:
                await interaction.response.send_message("❌ يرجى إدخال لون صحيح بتنسيق hex (مثال: #FF6B6B)", ephemeral=True)
                return

            # تحديث الـtheme الحالي
            current_theme = self.db.query(Theme).filter(Theme.name == "current").first()
            if not current_theme:
                current_theme = Theme(name="current")
                self.db.add(current_theme)
            
            current_theme.primary_color = color
            current_theme.font = font
            current_theme.icon_set = icon_set
            self.db.commit()
            
            embed = discord.Embed(
                title="🎨 تم تحديث المظهر",
                description="تم تحديث مظهر الـoverlay بنجاح",
                color=int(color[1:], 16)
            )
            embed.add_field(name="اللون الأساسي", value=color, inline=True)
            embed.add_field(name="الخط", value=font, inline=True)
            embed.add_field(name="الأيقونات", value=icon_set, inline=True)
            
            await interaction.response.send_message(embed=embed)
            
            # تحديث الـoverlay
            await self.bot.sio.emit('theme_updated', {
                'primary_color': color,
                'font': font,
                'icon_set': icon_set
            })
            
            # تسجيل الإجراء
            self.log_admin_action(
                "تحديث المظهر",
                interaction.user.display_name,
                f"اللون: {color}, الخط: {font}, الأيقونات: {icon_set}"
            )
            
        except Exception as e:
            await interaction.response.send_message(f"❌ خطأ في تحديث المظهر: {str(e)}", ephemeral=True)

    @app_commands.command(name="save_theme", description="حفظ المظهر الحالي")
    @app_commands.describe(name="اسم المظهر")
    async def save_theme(self, interaction: discord.Interaction, name: str):
        """حفظ المظهر الحالي"""
        if not self.is_admin(interaction):
            await interaction.response.send_message("❌ ليس لديك صلاحية لاستخدام هذا الأمر!", ephemeral=True)
            return

        try:
            current_theme = self.db.query(Theme).filter(Theme.name == "current").first()
            if not current_theme:
                await interaction.response.send_message("❌ لا يوجد مظهر حالي لحفظه!", ephemeral=True)
                return

            # حفظ المظهر باسم جديد
            saved_theme = Theme(
                name=name,
                primary_color=current_theme.primary_color,
                secondary_color=current_theme.secondary_color,
                font=current_theme.font,
                icon_set=current_theme.icon_set
            )
            self.db.add(saved_theme)
            self.db.commit()
            
            embed = discord.Embed(
                title="💾 تم حفظ المظهر",
                description=f"تم حفظ المظهر باسم **{name}**",
                color=0x00ff00
            )
            
            await interaction.response.send_message(embed=embed)
            
            # تسجيل الإجراء
            self.log_admin_action(
                "حفظ مظهر",
                interaction.user.display_name,
                f"اسم المظهر: {name}"
            )
            
        except Exception as e:
            await interaction.response.send_message(f"❌ خطأ في حفظ المظهر: {str(e)}", ephemeral=True)

    @app_commands.command(name="load_theme", description="تحميل مظهر محفوظ")
    @app_commands.describe(name="اسم المظهر")
    async def load_theme(self, interaction: discord.Interaction, name: str):
        """تحميل مظهر محفوظ"""
        if not self.is_admin(interaction):
            await interaction.response.send_message("❌ ليس لديك صلاحية لاستخدام هذا الأمر!", ephemeral=True)
            return

        try:
            theme = self.db.query(Theme).filter(Theme.name == name).first()
            if not theme:
                await interaction.response.send_message(f"❌ المظهر **{name}** غير موجود!", ephemeral=True)
                return

            # تحديث المظهر الحالي
            current_theme = self.db.query(Theme).filter(Theme.name == "current").first()
            if not current_theme:
                current_theme = Theme(name="current")
                self.db.add(current_theme)
            
            current_theme.primary_color = theme.primary_color
            current_theme.secondary_color = theme.secondary_color
            current_theme.font = theme.font
            current_theme.icon_set = theme.icon_set
            self.db.commit()
            
            embed = discord.Embed(
                title="📂 تم تحميل المظهر",
                description=f"تم تحميل المظهر **{name}**",
                color=int(theme.primary_color[1:], 16)
            )
            
            await interaction.response.send_message(embed=embed)
            
            # تحديث الـoverlay
            await self.bot.sio.emit('theme_updated', {
                'primary_color': theme.primary_color,
                'font': theme.font,
                'icon_set': theme.icon_set
            })
            
            # تسجيل الإجراء
            self.log_admin_action(
                "تحميل مظهر",
                interaction.user.display_name,
                f"اسم المظهر: {name}"
            )
            
        except Exception as e:
            await interaction.response.send_message(f"❌ خطأ في تحميل المظهر: {str(e)}", ephemeral=True)

    @app_commands.command(name="slot", description="الانضمام أو الانسحاب من جلسة")
    @app_commands.describe(call_name="اسم الجلسة")
    async def slot(self, interaction: discord.Interaction, call_name: str):
        """الانضمام أو الانسحاب من جلسة"""
        if not self.bot.current_session:
            await interaction.response.send_message("❌ لا توجد جلسة نشطة!", ephemeral=True)
            return

        if self.bot.current_session.status != 'open':
            await interaction.response.send_message("❌ التسجيل مغلق لهذه الجلسة!", ephemeral=True)
            return

        try:
            # التحقق من وجود المستخدم في الجلسة
            existing_participant = self.db.query(Participant).filter(
                Participant.session_id == self.bot.current_session.id,
                Participant.user_id == str(interaction.user.id)
            ).first()

            if existing_participant:
                # انسحاب من الجلسة
                self.db.delete(existing_participant)
                self.db.commit()
                
                embed = discord.Embed(
                    title="👋 تم الانسحاب",
                    description=f"انسحبت من جلسة **{call_name}**",
                    color=0xff6b6b
                )
                
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
                # تحديث الـoverlay
                await self.bot.sio.emit('participant_left', {
                    'session_id': self.bot.current_session.id,
                    'user_id': str(interaction.user.id),
                    'username': interaction.user.display_name
                })
                
            else:
                # انضمام للجلسة
                participant = Participant(
                    session_id=self.bot.current_session.id,
                    user_id=str(interaction.user.id),
                    username=interaction.user.display_name
                )
                self.db.add(participant)
                
                # تحديث إحصائيات المستخدم
                user_stats = self.db.query(UserStats).filter(UserStats.user_id == str(interaction.user.id)).first()
                if not user_stats:
                    user_stats = UserStats(
                        user_id=str(interaction.user.id),
                        username=interaction.user.display_name,
                        total_participations=1
                    )
                    self.db.add(user_stats)
                else:
                    user_stats.total_participations += 1
                
                self.db.commit()
                
                embed = discord.Embed(
                    title="✅ تم الانضمام",
                    description=f"انضممت لجلسة **{call_name}**",
                    color=0x00ff00
                )
                
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
                # تحديث الـoverlay
                await self.bot.sio.emit('participant_joined', {
                    'session_id': self.bot.current_session.id,
                    'user_id': str(interaction.user.id),
                    'username': interaction.user.display_name
                })
                
        except Exception as e:
            await interaction.response.send_message(f"❌ خطأ في الانضمام/الانسحاب: {str(e)}", ephemeral=True)

    @app_commands.command(name="my_stats", description="عرض إحصائياتي الشخصية")
    async def my_stats(self, interaction: discord.Interaction):
        """عرض إحصائيات المستخدم الشخصية"""
        try:
            user_stats = self.db.query(UserStats).filter(UserStats.user_id == str(interaction.user.id)).first()
            
            if not user_stats:
                embed = discord.Embed(
                    title="📊 إحصائياتي",
                    description="لا توجد إحصائيات متاحة",
                    color=0x808080
                )
            else:
                embed = discord.Embed(
                    title="📊 إحصائياتي",
                    description=f"إحصائيات **{interaction.user.display_name}**",
                    color=0x4ecdc4
                )
                embed.add_field(name="المشاركات", value=user_stats.total_participations, inline=True)
                embed.add_field(name="الانتصارات", value=user_stats.total_wins, inline=True)
                embed.add_field(name="النقاط", value=user_stats.points, inline=True)
                
                if user_stats.last_win:
                    embed.add_field(name="آخر فوز", value=user_stats.last_win.strftime("%Y-%m-%d %H:%M"), inline=True)
                
                # حساب نسبة الفوز
                if user_stats.total_participations > 0:
                    win_rate = (user_stats.total_wins / user_stats.total_participations) * 100
                    embed.add_field(name="نسبة الفوز", value=f"{win_rate:.1f}%", inline=True)
            
            await interaction.response.send_message(embed=embed, ephemeral=True)
            
        except Exception as e:
            await interaction.response.send_message(f"❌ خطأ في عرض الإحصائيات: {str(e)}", ephemeral=True) 