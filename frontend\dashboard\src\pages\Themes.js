import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Palette,
  Plus,
  Save,
  Trash2,
  Eye,
  Download,
  Upload,
  Settings,
  Paintbrush,
  Type,
  Image,
  Sparkles,
  RefreshCw
} from 'lucide-react';
import toast from 'react-hot-toast';
import { useTheme } from '../contexts/ThemeContext';
import ThemePreview, { ThemePreviewMini, OverlayThemePreview } from '../components/ThemePreview';

const Themes = () => {
  // Use theme context if available, otherwise fallback to local state
  let themeContext;
  try {
    themeContext = useTheme();
  } catch (error) {
    console.warn('ThemeContext not available, using fallback');
    themeContext = null;
  }

  const [localThemes, setLocalThemes] = useState([]);
  const [localCurrentTheme, setLocalCurrentTheme] = useState(null);
  const [localIsLoading, setLocalIsLoading] = useState(true);
  const [selectedTheme, setSelectedTheme] = useState(null);
  const [isCreating, setIsCreating] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  // Use context values if available, otherwise use local state
  const currentTheme = themeContext?.currentTheme || localCurrentTheme;
  const themes = themeContext?.themes || localThemes;
  const isLoading = themeContext?.isLoading ?? localIsLoading;

  // نموذج Theme جديد
  const [newTheme, setNewTheme] = useState({
    name: '',
    primary_color: '#FF6B6B',
    secondary_color: '#4ECDC4',
    font: 'Cairo',
    icon_set: 'default'
  });

  // خيارات الخطوط المتاحة
  const fontOptions = [
    { value: 'Cairo', label: 'Cairo - القاهرة', preview: 'font-cairo' },
    { value: 'Arial', label: 'Arial', preview: 'font-sans' },
    { value: 'Roboto', label: 'Roboto', preview: 'font-roboto' },
    { value: 'Poppins', label: 'Poppins', preview: 'font-poppins' },
    { value: 'Inter', label: 'Inter', preview: 'font-inter' }
  ];

  // خيارات مجموعات الأيقونات
  const iconSetOptions = [
    { value: 'default', label: 'افتراضي', description: 'أيقونات كلاسيكية' },
    { value: 'modern', label: 'عصري', description: 'أيقونات حديثة ومبسطة' },
    { value: 'gaming', label: 'ألعاب', description: 'أيقونات مخصصة للألعاب' },
    { value: 'neon', label: 'نيون', description: 'أيقونات متوهجة' }
  ];

  // Fallback API functions when context is not available
  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

  const fallbackFetchThemes = async () => {
    try {
      const response = await fetch(`${API_URL}/api/themes`);
      if (response.ok) {
        const data = await response.json();
        setLocalThemes(data.themes || []);
      }
    } catch (error) {
      toast.error('فشل في تحميل السمات');
    } finally {
      setLocalIsLoading(false);
    }
  };

  const fallbackFetchCurrentTheme = async () => {
    try {
      const response = await fetch(`${API_URL}/api/themes/current`);
      if (response.ok) {
        const theme = await response.json();
        setLocalCurrentTheme(theme);
        // Apply theme to DOM
        document.documentElement.style.setProperty('--primary-color', theme.primary_color);
        document.documentElement.style.setProperty('--secondary-color', theme.secondary_color);
        document.body.style.fontFamily = theme.font;
      }
    } catch (error) {
      console.error('خطأ في جلب السمة الحالية:', error);
    }
  };

  const fallbackApplyTheme = async (theme) => {
    try {
      const response = await fetch(`${API_URL}/api/themes/apply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          primary_color: theme.primary_color,
          secondary_color: theme.secondary_color,
          font: theme.font,
          icon_set: theme.icon_set
        }),
      });

      if (response.ok) {
        setLocalCurrentTheme(theme);
        document.documentElement.style.setProperty('--primary-color', theme.primary_color);
        document.documentElement.style.setProperty('--secondary-color', theme.secondary_color);
        document.body.style.fontFamily = theme.font;
        toast.success('تم تطبيق السمة بنجاح');
      } else {
        toast.error('فشل في تطبيق السمة');
      }
    } catch (error) {
      toast.error('خطأ في تطبيق السمة');
    }
  };

  // Load data on component mount
  useEffect(() => {
    if (!themeContext) {
      fallbackFetchThemes();
      fallbackFetchCurrentTheme();
    }
  }, [themeContext]);

  // إنشاء theme جديد
  const handleCreateTheme = async () => {
    if (!newTheme.name.trim()) {
      toast.error('يرجى إدخال اسم السمة');
      return;
    }

    try {
      if (themeContext) {
        await themeContext.createTheme(newTheme);
      } else {
        const response = await fetch(`${API_URL}/api/themes`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(newTheme),
        });
        if (response.ok) {
          toast.success('تم إنشاء السمة بنجاح');
          fallbackFetchThemes();
        } else {
          toast.error('فشل في إنشاء السمة');
        }
      }

      setIsCreating(false);
      setNewTheme({
        name: '',
        primary_color: '#FF6B6B',
        secondary_color: '#4ECDC4',
        font: 'Cairo',
        icon_set: 'default'
      });
    } catch (error) {
      toast.error('خطأ في إنشاء السمة');
    }
  };

  // تطبيق theme
  const handleApplyTheme = async (theme) => {
    if (themeContext) {
      await themeContext.applyTheme(theme);
    } else {
      await fallbackApplyTheme(theme);
    }
  };

  // حذف theme مع تأكيد
  const handleDeleteTheme = async (themeId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه السمة؟')) {
      return;
    }

    try {
      if (themeContext) {
        await themeContext.deleteTheme(themeId);
      } else {
        const response = await fetch(`${API_URL}/api/themes/${themeId}`, {
          method: 'DELETE',
        });
        if (response.ok) {
          toast.success('تم حذف السمة بنجاح');
          fallbackFetchThemes();
        } else {
          toast.error('فشل في حذف السمة');
        }
      }
    } catch (error) {
      toast.error('خطأ في حذف السمة');
    }
  };

  // استيراد السمات
  const handleImportThemes = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      if (themeContext) {
        await themeContext.importThemes(file);
      } else {
        toast.error('وظيفة الاستيراد تتطلب تفعيل ThemeContext');
      }
    } catch (error) {
      toast.error('فشل في استيراد السمات');
    }

    event.target.value = '';
  };

  // تصدير السمات
  const handleExportThemes = async () => {
    try {
      if (themeContext) {
        await themeContext.exportThemes();
      } else {
        // Fallback export
        const dataStr = JSON.stringify(themes, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `themes-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        toast.success('تم تصدير السمات بنجاح');
      }
    } catch (error) {
      toast.error('فشل في تصدير السمات');
    }
  };

  // إنشاء السمات الافتراضية
  const handleCreateDefaultThemes = async () => {
    try {
      if (themeContext) {
        await themeContext.createDefaultThemes();
      } else {
        toast.error('وظيفة السمات الافتراضية تتطلب تفعيل ThemeContext');
      }
    } catch (error) {
      toast.error('فشل في إنشاء السمات الافتراضية');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
            <Palette className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة السمات</h1>
            <p className="text-gray-600">تخصيص مظهر الموقع والـ Overlay</p>
          </div>
        </div>

        <div className="flex gap-3">
          {/* Export/Import */}
          <div className="flex gap-2">
            <button
              onClick={handleExportThemes}
              className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2"
              title="تصدير السمات"
            >
              <Download className="w-4 h-4" />
            </button>

            <label className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2 cursor-pointer">
              <Upload className="w-4 h-4" />
              <input
                type="file"
                accept=".json"
                onChange={handleImportThemes}
                className="hidden"
              />
            </label>
          </div>

          {/* Default Themes */}
          <button
            onClick={handleCreateDefaultThemes}
            className="px-4 py-2 bg-gradient-to-r from-green-600 to-teal-600 text-white rounded-lg hover:from-green-700 hover:to-teal-700 transition-all flex items-center gap-2"
            title="إنشاء سمات افتراضية"
          >
            <Sparkles className="w-4 h-4" />
            سمات افتراضية
          </button>

          {/* Preview Mode */}
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
              previewMode
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Eye className="w-4 h-4" />
            {previewMode ? 'إخفاء المعاينة' : 'معاينة'}
          </button>

          {/* Create New Theme */}
          <button
            onClick={() => setIsCreating(true)}
            className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            سمة جديدة
          </button>
        </div>
      </div>

      {/* Current Theme Display */}
      {currentTheme && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex gap-2">
                <div
                  className="w-8 h-8 rounded-full border-2 border-white shadow-lg"
                  style={{ backgroundColor: currentTheme.primary_color }}
                ></div>
                <div
                  className="w-8 h-8 rounded-full border-2 border-white shadow-lg"
                  style={{ backgroundColor: currentTheme.secondary_color }}
                ></div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">السمة الحالية</h3>
                <p className="text-gray-600">
                  الخط: {currentTheme.font} | الأيقونات: {currentTheme.icon_set}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 text-green-600">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">نشط</span>
            </div>
          </div>
        </motion.div>
      )}

      {/* Create New Theme Modal */}
      {isCreating && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">إنشاء سمة جديدة</h2>
              <button
                onClick={() => setIsCreating(false)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                ✕
              </button>
            </div>

            <div className="space-y-6">
              {/* Theme Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم السمة
                </label>
                <input
                  type="text"
                  value={newTheme.name}
                  onChange={(e) => setNewTheme({...newTheme, name: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="مثال: سمة الليل"
                />
              </div>

              {/* Colors */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اللون الأساسي
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={newTheme.primary_color}
                      onChange={(e) => setNewTheme({...newTheme, primary_color: e.target.value})}
                      className="w-12 h-12 rounded-lg border border-gray-300 cursor-pointer"
                    />
                    <input
                      type="text"
                      value={newTheme.primary_color}
                      onChange={(e) => setNewTheme({...newTheme, primary_color: e.target.value})}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="#FF6B6B"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اللون الثانوي
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={newTheme.secondary_color}
                      onChange={(e) => setNewTheme({...newTheme, secondary_color: e.target.value})}
                      className="w-12 h-12 rounded-lg border border-gray-300 cursor-pointer"
                    />
                    <input
                      type="text"
                      value={newTheme.secondary_color}
                      onChange={(e) => setNewTheme({...newTheme, secondary_color: e.target.value})}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="#4ECDC4"
                    />
                  </div>
                </div>
              </div>

              {/* Font Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نوع الخط
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {fontOptions.map((font) => (
                    <button
                      key={font.value}
                      onClick={() => setNewTheme({...newTheme, font: font.value})}
                      className={`p-3 text-right rounded-lg border-2 transition-all ${
                        newTheme.font === font.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className={`text-lg ${font.preview}`}>
                        {font.label}
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        نص تجريبي للخط
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Icon Set Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  مجموعة الأيقونات
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {iconSetOptions.map((iconSet) => (
                    <button
                      key={iconSet.value}
                      onClick={() => setNewTheme({...newTheme, icon_set: iconSet.value})}
                      className={`p-4 text-right rounded-lg border-2 transition-all ${
                        newTheme.icon_set === iconSet.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <Image className="w-5 h-5 text-gray-600" />
                        <div>
                          <div className="font-medium">{iconSet.label}</div>
                          <div className="text-sm text-gray-500">{iconSet.description}</div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Preview */}
              <div className="p-4 rounded-lg border border-gray-200 bg-gray-50">
                <h4 className="text-sm font-medium text-gray-700 mb-3">معاينة السمة</h4>
                <div
                  className="p-4 rounded-lg text-white"
                  style={{
                    background: `linear-gradient(135deg, ${newTheme.primary_color}, ${newTheme.secondary_color})`,
                    fontFamily: newTheme.font
                  }}
                >
                  <div className="text-lg font-bold mb-2">عنوان تجريبي</div>
                  <div className="text-sm opacity-90">هذا نص تجريبي لمعاينة السمة الجديدة</div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={() => setIsCreating(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleCreateTheme}
                className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all flex items-center gap-2"
              >
                <Save className="w-4 h-4" />
                حفظ السمة
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Preview Mode - Overlay Preview */}
      {previewMode && currentTheme && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">معاينة الـ Overlay</h3>
          <OverlayThemePreview theme={currentTheme} />
        </motion.div>
      )}

      {/* Themes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {themes.map((theme) => {
          const isActive = currentTheme &&
            currentTheme.primary_color === theme.primary_color &&
            currentTheme.secondary_color === theme.secondary_color;

          return (
            <motion.div
              key={theme.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="relative"
            >
              <ThemePreview
                theme={theme}
                isActive={isActive}
                onApply={handleApplyTheme}
              />

              {/* Action Buttons */}
              <div className="absolute top-2 left-2 flex gap-1">
                <button
                  onClick={() => setSelectedTheme(theme)}
                  className="p-1.5 bg-white bg-opacity-90 text-gray-700 rounded-lg hover:bg-opacity-100 transition-all shadow-sm"
                  title="عرض التفاصيل"
                >
                  <Eye className="w-3 h-3" />
                </button>

                <button
                  onClick={() => handleDeleteTheme(theme.id)}
                  className="p-1.5 bg-white bg-opacity-90 text-red-600 rounded-lg hover:bg-opacity-100 transition-all shadow-sm"
                  title="حذف السمة"
                >
                  <Trash2 className="w-3 h-3" />
                </button>
              </div>
            </motion.div>
          );
        })}

        {/* Empty State */}
        {themes.length === 0 && (
          <div className="col-span-full text-center py-12">
            <Palette className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد سمات محفوظة</h3>
            <p className="text-gray-600 mb-4">ابدأ بإنشاء سمة جديدة لتخصيص مظهر الموقع</p>
            <button
              onClick={() => setIsCreating(true)}
              className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all flex items-center gap-2 mx-auto"
            >
              <Plus className="w-4 h-4" />
              إنشاء سمة جديدة
            </button>
          </div>
        )}
      </div>

      {/* Theme Details Modal */}
      {selectedTheme && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl p-6 w-full max-w-lg"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">تفاصيل السمة</h2>
              <button
                onClick={() => setSelectedTheme(null)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              {/* Theme Preview */}
              <OverlayThemePreview theme={selectedTheme} className="mb-4" />

              {/* Theme Details */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اللون الأساسي
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className="w-6 h-6 rounded border border-gray-300"
                      style={{ backgroundColor: selectedTheme.primary_color }}
                    ></div>
                    <span className="text-sm text-gray-600">{selectedTheme.primary_color}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اللون الثانوي
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className="w-6 h-6 rounded border border-gray-300"
                      style={{ backgroundColor: selectedTheme.secondary_color }}
                    ></div>
                    <span className="text-sm text-gray-600">{selectedTheme.secondary_color}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    نوع الخط
                  </label>
                  <span className="text-sm text-gray-600">{selectedTheme.font}</span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    مجموعة الأيقونات
                  </label>
                  <span className="text-sm text-gray-600">{selectedTheme.icon_set}</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  تاريخ الإنشاء
                </label>
                <span className="text-sm text-gray-600">
                  {new Date(selectedTheme.created_at).toLocaleString('ar-SA')}
                </span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={() => setSelectedTheme(null)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                إغلاق
              </button>
              <button
                onClick={() => {
                  handleApplyTheme(selectedTheme);
                  setSelectedTheme(null);
                }}
                className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all flex items-center gap-2"
              >
                <Paintbrush className="w-4 h-4" />
                تطبيق السمة
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
};

export default Themes;