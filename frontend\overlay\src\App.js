import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { io } from 'socket.io-client';
import { 
  Users, 
  Trophy, 
  Gamepad2, 
  Crown, 
  Star,
  Zap,
  Target,
  Award
} from 'lucide-react';

// مكونات الـoverlay
import SessionInfo from './components/SessionInfo';
import ParticipantsList from './components/ParticipantsList';
import WinnerDisplay from './components/WinnerDisplay';
import GameAnimation from './components/GameAnimation';
import Leaderboard from './components/Leaderboard';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

function App() {
  const [socket, setSocket] = useState(null);
  const [currentSession, setCurrentSession] = useState(null);
  const [participants, setParticipants] = useState([]);
  const [currentWinner, setCurrentWinner] = useState(null);
  const [theme, setTheme] = useState({
    primary_color: '#FF6B6B',
    secondary_color: '#4ECDC4',
    font: 'Cairo',
    icon_set: 'default'
  });
  const [isConnected, setIsConnected] = useState(false);
  const [showWinner, setShowWinner] = useState(false);
  const [showLeaderboard, setShowLeaderboard] = useState(false);

  useEffect(() => {
    // إنشاء اتصال Socket.IO
    const newSocket = io(API_URL);
    setSocket(newSocket);

    // استقبال الأحداث
    newSocket.on('connect', () => {
      console.log('متصل بالخادم');
      setIsConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('انقطع الاتصال');
      setIsConnected(false);
    });

    newSocket.on('session_started', (data) => {
      console.log('جلسة جديدة بدأت:', data);
      setCurrentSession(data);
      setParticipants([]);
      setCurrentWinner(null);
    });

    newSocket.on('session_closed', (data) => {
      console.log('الجلسة أغلقت:', data);
      if (currentSession && currentSession.id === data.session_id) {
        setCurrentSession(prev => ({ ...prev, status: 'closed' }));
      }
    });

    newSocket.on('participant_joined', (data) => {
      console.log('مشارك انضم:', data);
      setParticipants(prev => [...prev, data]);
    });

    newSocket.on('participant_left', (data) => {
      console.log('مشارك انسحب:', data);
      setParticipants(prev => prev.filter(p => p.user_id !== data.user_id));
    });

    newSocket.on('winner_picked', (data) => {
      console.log('فائز جديد:', data);
      setCurrentWinner(data.winner);
      setShowWinner(true);
      
      // إخفاء الفائز بعد 5 ثواني
      setTimeout(() => {
        setShowWinner(false);
      }, 5000);
    });

    newSocket.on('theme_updated', (data) => {
      console.log('تم تحديث المظهر:', data);
      setTheme(data);
    });

    newSocket.on('sessions_reset', () => {
      console.log('تم إعادة تعيين الجلسات');
      setCurrentSession(null);
      setParticipants([]);
      setCurrentWinner(null);
    });

    // تنظيف الاتصال عند إغلاق المكون
    return () => {
      newSocket.close();
    };
  }, []);

  // تحميل البيانات الأولية
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        // تحميل الجلسة الحالية
        const sessionResponse = await fetch(`${API_URL}/api/sessions/current`);
        if (sessionResponse.ok) {
          const sessionData = await sessionResponse.json();
          if (sessionData.session) {
            setCurrentSession(sessionData.session);
            setParticipants(sessionData.participants);
          }
        }

        // تحميل المظهر الحالي
        const themeResponse = await fetch(`${API_URL}/api/themes/current`);
        if (themeResponse.ok) {
          const themeData = await themeResponse.json();
          setTheme(themeData);
        }
      } catch (error) {
        console.error('خطأ في تحميل البيانات الأولية:', error);
      }
    };

    fetchInitialData();
  }, []);

  // تطبيق المظهر
  useEffect(() => {
    document.documentElement.style.setProperty('--primary-color', theme.primary_color);
    document.documentElement.style.setProperty('--secondary-color', theme.secondary_color);
    document.body.style.fontFamily = theme.font;
  }, [theme]);

  if (!isConnected) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p>جاري الاتصال بالخادم...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="overlay-container w-screen h-screen relative overflow-hidden">
      {/* مؤشر الاتصال */}
      <div className="absolute top-4 left-4 z-50">
        <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'} animate-pulse`}></div>
      </div>

      {/* الجلسة الحالية */}
      {currentSession && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-4 right-4 z-40"
        >
          <SessionInfo session={currentSession} theme={theme} />
        </motion.div>
      )}

      {/* قائمة المشاركين */}
      {currentSession && participants.length > 0 && (
        <motion.div
          initial={{ opacity: 0, x: 100 }}
          animate={{ opacity: 1, x: 0 }}
          className="absolute top-20 right-4 z-30"
        >
          <ParticipantsList 
            participants={participants} 
            theme={theme}
            maxHeight="60vh"
          />
        </motion.div>
      )}

      {/* عرض الفائز */}
      <AnimatePresence>
        {showWinner && currentWinner && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            className="absolute inset-0 flex items-center justify-center z-50"
          >
            <WinnerDisplay 
              winner={currentWinner} 
              session={currentSession}
              theme={theme}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* أنيميشن اللعبة */}
      {currentSession && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute bottom-4 left-4 z-30"
        >
          <GameAnimation 
            gameType={currentSession.game_type}
            isActive={currentSession.status === 'open'}
            theme={theme}
          />
        </motion.div>
      )}

      {/* الليدر بورد */}
      <motion.div
        initial={{ opacity: 0, x: -100 }}
        animate={{ opacity: 1, x: 0 }}
        className="absolute bottom-4 right-4 z-30"
      >
        <Leaderboard theme={theme} />
      </motion.div>

      {/* أزرار التحكم */}
      <div className="absolute top-4 left-20 z-40 flex gap-2">
        <button
          onClick={() => setShowLeaderboard(!showLeaderboard)}
          className="button-hover bg-white bg-opacity-20 backdrop-blur-sm rounded-lg p-2 text-white hover:bg-opacity-30 transition-all"
          title="إظهار/إخفاء الليدر بورد"
        >
          <Trophy size={20} />
        </button>
        
        <button
          onClick={() => window.location.reload()}
          className="button-hover bg-white bg-opacity-20 backdrop-blur-sm rounded-lg p-2 text-white hover:bg-opacity-30 transition-all"
          title="إعادة تحميل"
        >
          <Zap size={20} />
        </button>
      </div>

      {/* رسالة عند عدم وجود جلسة نشطة */}
      {!currentSession && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 flex items-center justify-center"
        >
          <div className="text-center text-white">
            <Gamepad2 size={64} className="mx-auto mb-4 opacity-50" />
            <h2 className="text-2xl font-bold mb-2">لا توجد جلسة نشطة</h2>
            <p className="text-lg opacity-75">في انتظار بدء جلسة جديدة...</p>
          </div>
        </motion.div>
      )}

      {/* إحصائيات سريعة */}
      {currentSession && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-4 left-1/2 transform -translate-x-1/2 z-30"
        >
          <div className="flex gap-4 text-white">
            <div className="flex items-center gap-2 bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-3 py-1">
              <Users size={16} />
              <span className="font-bold">{participants.length}</span>
            </div>
            
            {currentSession.status === 'closed' && (
              <div className="flex items-center gap-2 bg-yellow-500 bg-opacity-80 backdrop-blur-sm rounded-lg px-3 py-1">
                <Target size={16} />
                <span className="font-bold">مغلق</span>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
}

export default App; 