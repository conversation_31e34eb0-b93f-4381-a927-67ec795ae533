# دليل التطوير - نظام إدارة الكازينو

## 🛠️ أدوات التطوير الموصى بها

### React DevTools
لتحسين تجربة التطوير، قم بتثبيت React DevTools:

#### لمتصفح Chrome:
1. اذهب إلى [Chrome Web Store](https://chrome.google.com/webstore/detail/react-developer-tools/fmkadmapgofadopljbjfkapdkoienihi)
2. اضغط على "إضافة إلى Chrome"
3. أعد تشغيل المتصفح

#### لمتصفح Firefox:
1. اذهب إلى [Firefox Add-ons](https://addons.mozilla.org/en-US/firefox/addon/react-devtools/)
2. اضغط على "Add to Firefox"
3. أعد تشغيل المتصفح

### VS Code Extensions الموصى بها:
- **ES7+ React/Redux/React-Native snippets**
- **Tailwind CSS IntelliSense**
- **Prettier - Code formatter**
- **ESLint**

## 🚀 تشغيل المشروع

```bash
# تثبيت التبعيات
npm install

# تشغيل في وضع التطوير
npm start

# بناء للمنتج
npm run build
```

## 📁 هيكل المشروع

```
src/
├── components/     # المكونات المشتركة
├── pages/         # صفحات التطبيق
├── services/      # خدمات API
└── shared/        # الملفات المشتركة
```

## 🎨 التصميم

المشروع يستخدم:
- **Tailwind CSS** للتصميم
- **Framer Motion** للحركات
- **Lucide React** للأيقونات
- **React Hot Toast** للإشعارات

## 🔧 إعدادات التطوير

### React Router Warnings
تم إضافة future flags لتجنب التحذيرات:
```javascript
const router = {
  future: {
    v7_startTransition: true,
    v7_relativeSplatPath: true
  }
};
```

### RTL Support
التطبيق يدعم اللغة العربية والتصميم من اليمين لليسار (RTL).

## 📝 ملاحظات التطوير

- جميع النصوص باللغة العربية
- التصميم متجاوب مع جميع الأجهزة
- يدعم الوضع المظلم (Dark Mode)
- متوافق مع معايير الوصول (Accessibility)

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:
1. **خطأ في تحميل الصور**: استخدم placeholders بدلاً من الصور غير الموجودة
2. **تحذيرات React Router**: تم حلها باستخدام future flags
3. **مشاكل RTL**: تأكد من استخدام `space-x-reverse` في Tailwind

## 📞 الدعم

للمساعدة التقنية، راجع:
- [React Documentation](https://reactjs.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Framer Motion](https://www.framer.com/motion/) 