import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Crown, Trophy, Star, Zap, Award } from 'lucide-react';

const WinnerDisplay = ({ winner, session, theme }) => {
  const [showConfetti, setShowConfetti] = useState(false);
  const [audio, setAudio] = useState(null);

  useEffect(() => {
    // تشغيل صوت الفوز
    if (audio) {
      audio.play().catch(e => console.log('لا يمكن تشغيل الصوت:', e));
    }

    // إظهار الكونفيتي
    setShowConfetti(true);
    
    // إخفاء الكونفيتي بعد 3 ثواني
    const timer = setTimeout(() => {
      setShowConfetti(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, [winner, audio]);

  const getGameIcon = (gameType) => {
    switch (gameType) {
      case 'slots':
        return '🎰';
      case 'roulette':
        return '🎲';
      case 'wheel':
        return '🎡';
      default:
        return '🎮';
    }
  };

  const getGameName = (gameType) => {
    switch (gameType) {
      case 'slots':
        return 'سلوتس';
      case 'roulette':
        return 'روليت';
      case 'wheel':
        return 'عجلة الحظ';
      default:
        return 'لعبة';
    }
  };

  return (
    <div className="relative w-full h-full flex items-center justify-center">
      {/* خلفية شفافة */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
      />

      {/* الكونفيتي */}
      {showConfetti && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(50)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ 
                x: Math.random() * window.innerWidth,
                y: -20,
                rotate: 0
              }}
              animate={{ 
                y: window.innerHeight + 20,
                rotate: 360
              }}
              transition={{ 
                duration: Math.random() * 3 + 2,
                ease: "linear"
              }}
              className="absolute w-2 h-2"
              style={{
                backgroundColor: [
                  '#FF6B6B',
                  '#4ECDC4',
                  '#FFD93D',
                  '#FF8E53',
                  '#6C5CE7'
                ][Math.floor(Math.random() * 5)],
                left: `${Math.random() * 100}%`
              }}
            />
          ))}
        </div>
      )}

      {/* بطاقة الفائز */}
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        className="winner-celebration relative z-10 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rounded-2xl p-8 text-center shadow-2xl"
        style={{
          boxShadow: `0 0 50px ${theme.primary_color}`,
          minWidth: '400px'
        }}
      >
        {/* تاج الفائز */}
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="mb-4"
        >
          <Crown size={64} className="mx-auto text-white drop-shadow-lg" />
        </motion.div>

        {/* رسالة التهنئة */}
        <motion.h1
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-3xl font-bold text-white mb-2 text-glow"
        >
          مبروك! 🎉
        </motion.h1>

        {/* اسم الفائز */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.7 }}
          className="mb-4"
        >
          <h2 className="text-4xl font-bold text-white mb-2 text-bounce">
            {winner.username}
          </h2>
          <p className="text-xl text-white opacity-90">
            فاز في جلسة {session.name}
          </p>
        </motion.div>

        {/* تفاصيل اللعبة */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.9 }}
          className="flex items-center justify-center gap-4 mb-6"
        >
          <div className="text-4xl">{getGameIcon(session.game_type)}</div>
          <div className="text-white">
            <p className="text-lg font-semibold">{getGameName(session.game_type)}</p>
            <p className="text-sm opacity-75">+10 نقاط</p>
          </div>
        </motion.div>

        {/* أزرار التأثيرات */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 1.1 }}
          className="flex justify-center gap-4"
        >
          <div className="flex items-center gap-2 text-white">
            <Trophy size={20} />
            <span className="font-semibold">الفائز</span>
          </div>
          <div className="flex items-center gap-2 text-white">
            <Star size={20} />
            <span className="font-semibold">مبروك</span>
          </div>
          <div className="flex items-center gap-2 text-white">
            <Award size={20} />
            <span className="font-semibold">أحسنت</span>
          </div>
        </motion.div>

        {/* تأثيرات إضافية */}
        <motion.div
          animate={{ 
            boxShadow: [
              `0 0 30px ${theme.primary_color}`,
              `0 0 60px ${theme.primary_color}`,
              `0 0 30px ${theme.primary_color}`
            ]
          }}
          transition={{ duration: 2, repeat: Infinity }}
          className="absolute inset-0 rounded-2xl pointer-events-none"
        />
      </motion.div>

      {/* تأثيرات صوتية (اختيارية) */}
      <audio
        ref={setAudio}
        src="/sounds/winner.mp3"
        preload="auto"
        style={{ display: 'none' }}
      />
    </div>
  );
};

export default WinnerDisplay; 