@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Arial', sans-serif;
  background: transparent;
  overflow: hidden;
  direction: rtl;
}

#root {
  background: transparent;
  width: 100vw;
  height: 100vh;
}

/* تأثيرات بصرية */
.glow {
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* تأثيرات خاصة للألعاب */
.slot-spin {
  animation: slotSpin 1s ease-in-out;
}

@keyframes slotSpin {
  0% {
    transform: translateY(-100px) rotate(0deg);
  }
  50% {
    transform: translateY(0) rotate(180deg);
  }
  100% {
    transform: translateY(0) rotate(360deg);
  }
}

.wheel-spin {
  animation: wheelSpin 2s ease-out;
}

@keyframes wheelSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(720deg);
  }
}

.roulette-flash {
  animation: rouletteFlash 0.5s ease-in-out;
}

@keyframes rouletteFlash {
  0%, 100% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(255, 215, 0, 0.3);
  }
}

/* تأثير الفائز */
.winner-celebration {
  animation: winnerCelebration 1s ease-in-out;
}

@keyframes winnerCelebration {
  0% {
    transform: scale(0.5) rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(360deg);
    opacity: 1;
  }
}

/* تأثيرات النص */
.text-glow {
  text-shadow: 0 0 10px currentColor;
}

.text-bounce {
  animation: textBounce 0.6s ease-in-out;
}

@keyframes textBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* تحسين الأداء */
.overlay-container {
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* تأثيرات الخلفية */
.gradient-bg {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.9), rgba(78, 205, 196, 0.9));
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* تأثيرات الأزرار */
.button-hover {
  transition: all 0.3s ease;
}

.button-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* تأثيرات القوائم */
.list-item {
  transition: all 0.2s ease;
}

.list-item:hover {
  transform: translateX(-5px);
  background: rgba(255, 255, 255, 0.1);
}

/* تأثيرات الأيقونات */
.icon-spin {
  animation: iconSpin 1s linear infinite;
}

@keyframes iconSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* تأثيرات العدادات */
.counter {
  animation: counterPulse 0.5s ease-in-out;
}

@keyframes counterPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
} 