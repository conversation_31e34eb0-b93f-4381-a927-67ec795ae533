FROM python:3.11-slim

# تعيين مجلد العمل
WORKDIR /app

# تثبيت المتطلبات النظامية
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملفات المتطلبات
COPY requirements.txt .

# تثبيت متطلبات Python
RUN pip install --no-cache-dir -r requirements.txt

# نسخ الكود
COPY . .

# إنشاء مجلد قاعدة البيانات
RUN mkdir -p database

# تعيين الصلاحيات
RUN chmod +x main.py

# فتح المنفذ
EXPOSE 8000

# تشغيل التطبيق
CMD ["python", "main.py"] 