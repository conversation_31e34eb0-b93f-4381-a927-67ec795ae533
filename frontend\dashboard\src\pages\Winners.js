import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, DollarSign, Calendar, User, Star, Filter } from 'lucide-react';
import toast from 'react-hot-toast';

const Winners = () => {
  const [winners, setWinners] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');

  useEffect(() => {
    // Simulate loading winners data
    setTimeout(() => {
      setWinners([
        {
          id: 1,
          playerName: 'أحمد محمد',
          playerId: 'P001',
          game: 'Slot Machine',
          amount: 5000,
          date: '2024-01-15T14:30:00',
          session: 'جلسة الصباح',
          multiplier: 50,
          betAmount: 100
        },
        {
          id: 2,
          playerName: 'فاطمة علي',
          playerId: 'P002',
          game: 'Roulette',
          amount: 3200,
          date: '2024-01-15T16:45:00',
          session: 'جلسة المساء',
          multiplier: 32,
          betAmount: 100
        },
        {
          id: 3,
          playerName: 'محمد حسن',
          playerId: 'P003',
          game: 'Blackjack',
          amount: 1800,
          date: '2024-01-15T12:15:00',
          session: 'جلسة الصباح',
          multiplier: 18,
          betAmount: 100
        },
        {
          id: 4,
          playerName: 'سارة أحمد',
          playerId: 'P004',
          game: 'Slot Machine',
          amount: 7500,
          date: '2024-01-14T22:30:00',
          session: 'جلسة الليل',
          multiplier: 75,
          betAmount: 100
        },
        {
          id: 5,
          playerName: 'علي محمود',
          playerId: 'P005',
          game: 'Poker',
          amount: 4200,
          date: '2024-01-14T20:15:00',
          session: 'جلسة الليل',
          multiplier: 42,
          betAmount: 100
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredAndSortedWinners = winners
    .filter(winner => {
      if (filter === 'all') return true;
      if (filter === 'today') {
        const today = new Date().toDateString();
        return new Date(winner.date).toDateString() === today;
      }
      if (filter === 'week') {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return new Date(winner.date) >= weekAgo;
      }
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'amount':
          return b.amount - a.amount;
        case 'date':
          return new Date(b.date) - new Date(a.date);
        case 'multiplier':
          return b.multiplier - a.multiplier;
        default:
          return 0;
      }
    });

  const totalWinnings = winners.reduce((sum, winner) => sum + winner.amount, 0);
  const todayWinnings = winners.filter(winner => 
    new Date(winner.date).toDateString() === new Date().toDateString()
  ).reduce((sum, winner) => sum + winner.amount, 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الفائزين</h1>
          <p className="text-gray-600 mt-1">عرض وتتبع الفائزين في الألعاب</p>
        </div>
        <div className="mt-4 sm:mt-0 flex gap-2">
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
            تصدير البيانات
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">إجمالي الفوز</p>
              <p className="text-2xl font-bold text-gray-900">${totalWinnings.toLocaleString()}</p>
            </div>
            <Trophy className="w-8 h-8 text-yellow-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2, delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">فوز اليوم</p>
              <p className="text-2xl font-bold text-gray-900">${todayWinnings.toLocaleString()}</p>
            </div>
            <DollarSign className="w-8 h-8 text-green-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2, delay: 0.2 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">عدد الفائزين</p>
              <p className="text-2xl font-bold text-gray-900">{winners.length}</p>
            </div>
            <Star className="w-8 h-8 text-blue-500" />
          </div>
        </motion.div>
      </div>

      {/* Filters and Sort */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex flex-wrap gap-2">
            {['all', 'today', 'week'].map((filterOption) => (
              <button
                key={filterOption}
                onClick={() => setFilter(filterOption)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  filter === filterOption
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {filterOption === 'all' ? 'الكل' : 
                 filterOption === 'today' ? 'اليوم' : 'الأسبوع'}
              </button>
            ))}
          </div>
          
          <div className="flex items-center space-x-2 space-x-reverse">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="date">ترتيب حسب التاريخ</option>
              <option value="amount">ترتيب حسب المبلغ</option>
              <option value="multiplier">ترتيب حسب المضاعف</option>
            </select>
          </div>
        </div>
      </div>

      {/* Winners Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  اللاعب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  اللعبة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المبلغ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المضاعف
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الجلسة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التاريخ
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAndSortedWinners.map((winner, index) => (
                <motion.tr
                  key={winner.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2, delay: index * 0.05 }}
                  className="hover:bg-gray-50"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <User className="w-5 h-5 text-blue-600" />
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{winner.playerName}</div>
                        <div className="text-sm text-gray-500">{winner.playerId}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      {winner.game}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-green-600">
                      ${winner.amount.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      رهان: ${winner.betAmount}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      {winner.multiplier}x
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {winner.session}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center space-x-1 space-x-reverse">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(winner.date).toLocaleDateString('ar-SA')}</span>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {filteredAndSortedWinners.length === 0 && (
        <div className="text-center py-12">
          <Trophy className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">لا توجد فائزين متاحين</p>
        </div>
      )}
    </motion.div>
  );
};

export default Winners; 