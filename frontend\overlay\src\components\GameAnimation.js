import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Play, Pause, RotateCcw } from 'lucide-react';

const GameAnimation = ({ gameType, isActive, theme }) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationKey, setAnimationKey] = useState(0);

  useEffect(() => {
    if (isActive && !isAnimating) {
      setIsAnimating(true);
    } else if (!isActive) {
      setIsAnimating(false);
    }
  }, [isActive, isAnimating]);

  const getGameIcon = (gameType) => {
    switch (gameType) {
      case 'slots':
        return '🎰';
      case 'roulette':
        return '🎲';
      case 'wheel':
        return '🎡';
      default:
        return '🎮';
    }
  };

  const getGameName = (gameType) => {
    switch (gameType) {
      case 'slots':
        return 'سلوتس';
      case 'roulette':
        return 'روليت';
      case 'wheel':
        return 'عجلة الحظ';
      default:
        return 'لعبة';
    }
  };

  const getAnimationClass = (gameType) => {
    switch (gameType) {
      case 'slots':
        return 'slot-spin';
      case 'roulette':
        return 'roulette-flash';
      case 'wheel':
        return 'wheel-spin';
      default:
        return '';
    }
  };

  const handleAnimationToggle = () => {
    if (isActive) {
      setIsAnimating(!isAnimating);
    }
  };

  const handleRestartAnimation = () => {
    setAnimationKey(prev => prev + 1);
    setIsAnimating(true);
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className="glass-effect rounded-xl p-4 min-w-[200px]"
      style={{
        borderColor: theme.primary_color,
        boxShadow: `0 0 20px ${theme.primary_color}40`
      }}
    >
      {/* عنوان اللعبة */}
      <div className="text-center mb-4">
        <h3 className="text-lg font-bold text-white mb-2">
          {getGameName(gameType)}
        </h3>
        <div className="text-4xl mb-2">
          {getGameIcon(gameType)}
        </div>
      </div>

      {/* أنيميشن اللعبة */}
      <div className="relative h-24 mb-4 flex items-center justify-center">
        <motion.div
          key={animationKey}
          className={`text-6xl ${getAnimationClass(gameType)}`}
          animate={isAnimating ? {
            scale: [1, 1.1, 1],
            rotate: gameType === 'wheel' ? [0, 360] : [0, 10, -10, 0]
          } : {}}
          transition={{
            duration: gameType === 'wheel' ? 2 : 1,
            repeat: isAnimating ? Infinity : 0,
            ease: "easeInOut"
          }}
        >
          {getGameIcon(gameType)}
        </motion.div>

        {/* تأثيرات إضافية */}
        {isAnimating && (
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute inset-0 rounded-full"
            style={{
              background: `radial-gradient(circle, ${theme.primary_color}20, transparent)`
            }}
          />
        )}
      </div>

      {/* حالة اللعبة */}
      <div className="text-center mb-4">
        <div className="flex items-center justify-center gap-2">
          <div className={`w-3 h-3 rounded-full ${isActive ? 'bg-green-500' : 'bg-red-500'} animate-pulse`}></div>
          <span className="text-white text-sm font-medium">
            {isActive ? 'نشطة' : 'متوقفة'}
          </span>
        </div>
      </div>

      {/* أزرار التحكم */}
      <div className="flex justify-center gap-2">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={handleAnimationToggle}
          disabled={!isActive}
          className={`p-2 rounded-lg transition-all ${
            isActive 
              ? 'bg-white bg-opacity-20 hover:bg-opacity-30 text-white' 
              : 'bg-gray-500 text-gray-300 cursor-not-allowed'
          }`}
          title={isAnimating ? 'إيقاف الأنيميشن' : 'تشغيل الأنيميشن'}
        >
          {isAnimating ? <Pause size={16} /> : <Play size={16} />}
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={handleRestartAnimation}
          disabled={!isActive}
          className={`p-2 rounded-lg transition-all ${
            isActive 
              ? 'bg-white bg-opacity-20 hover:bg-opacity-30 text-white' 
              : 'bg-gray-500 text-gray-300 cursor-not-allowed'
          }`}
          title="إعادة تشغيل الأنيميشن"
        >
          <RotateCcw size={16} />
        </motion.button>
      </div>

      {/* تأثيرات بصرية إضافية */}
      {isAnimating && (
        <motion.div
          animate={{ 
            boxShadow: [
              `0 0 10px ${theme.primary_color}40`,
              `0 0 20px ${theme.primary_color}60`,
              `0 0 10px ${theme.primary_color}40`
            ]
          }}
          transition={{ duration: 2, repeat: Infinity }}
          className="absolute inset-0 rounded-xl pointer-events-none"
        />
      )}

      {/* جزيئات متحركة */}
      {isAnimating && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ 
                x: Math.random() * 200,
                y: Math.random() * 200,
                opacity: 0
              }}
              animate={{ 
                x: Math.random() * 200,
                y: Math.random() * 200,
                opacity: [0, 1, 0]
              }}
              transition={{ 
                duration: Math.random() * 2 + 1,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="absolute w-1 h-1 rounded-full"
              style={{
                backgroundColor: theme.secondary_color
              }}
            />
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default GameAnimation; 