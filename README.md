# نظام إدارة جلسات ألعاب الكازينو - Discord Bot

نظام متكامل لإدارة وعرض جلسات ألعاب الكازينو (Slot Calls, Roulette, Wheel) على ديسكورد مع تكامل مباشر مع OBS عبر Overlay تفاعلي، وواجهة تحكم إدارية مستقلة.

## المميزات

### 🎮 ألعاب مدعومة
- **Slot Calls**: جلسات سلوتس تفاعلية
- **Roulette**: روليت كلاسيكية
- **Wheel**: عجلة الحظ

### 🤖 بوت ديسكورد
- أوامر إدارية متقدمة
- نظام صلاحيات مرن
- إحصائيات مفصلة للمستخدمين

### 🎥 OBS Overlay
- تحديث مباشر للبيانات
- مؤثرات بصرية وصوتية
- دعم Themes مخصصة

### 🛠️ لوحة تحكم إدارية
- واجهة ويب متقدمة
- إدارة الجلسات والمستخدمين
- تصدير البيانات والإحصائيات

## التثبيت والتشغيل

### المتطلبات
- Python 3.8+
- Node.js 16+
- Discord Bot Token
- Discord OAuth2 Credentials

### التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd ISlotcall
```

2. **إعداد البيئة الافتراضية**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
cd frontend && npm install
```

4. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
# تعديل .env بالمعلومات المطلوبة
```

5. **تشغيل النظام**
```bash
# تشغيل الباك إند
python main.py

# تشغيل الفرونت إند (في terminal منفصل)
cd frontend && npm start
```

## الأوامر المتاحة

### أوامر الأدمن
- `/start_slot` - بدء جلسة جديدة
- `/reset_slot` - إعادة تعيين جميع الجلسات
- `/slot_off` - إغلاق التسجيل
- `/pick_winner` - اختيار فائز
- `/set_theme` - تخصيص المظهر
- `/save_theme` - حفظ Theme
- `/load_theme` - تحميل Theme

### أوامر المستخدم
- `/slot` - الانضمام/الانسحاب من جلسة
- `/my_stats` - عرض الإحصائيات الشخصية

## هيكل المشروع

```
ISlotcall/
├── backend/
│   ├── bot/
│   ├── api/
│   ├── database/
│   └── utils/
├── frontend/
│   ├── overlay/
│   ├── dashboard/
│   └── shared/
├── database/
└── docs/
```

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. 