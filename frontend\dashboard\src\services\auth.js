const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

export const checkAuthStatus = async () => {
  try {
    // التحقق من وجود بيانات المستخدم في localStorage
    const user = localStorage.getItem('user');
    const isAuthenticated = localStorage.getItem('isAuthenticated');
    
    if (user && isAuthenticated === 'true') {
      return {
        isAuthenticated: true,
        user: JSON.parse(user)
      };
    }
    
    return {
      isAuthenticated: false,
      user: null
    };
  } catch (error) {
    console.error('خطأ في التحقق من حالة المصادقة:', error);
    return {
      isAuthenticated: false,
      user: null
    };
  }
};

export const login = async (credentials) => {
  try {
    // في التطبيق الحقيقي، سيتم إرسال البيانات إلى API
    const response = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    if (response.ok) {
      const data = await response.json();
      return data;
    } else {
      throw new Error('فشل في تسجيل الدخول');
    }
  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    throw error;
  }
};

export const logout = () => {
  try {
    // حذف بيانات المستخدم من localStorage
    localStorage.removeItem('user');
    localStorage.removeItem('isAuthenticated');
    
    return { success: true };
  } catch (error) {
    console.error('خطأ في تسجيل الخروج:', error);
    throw error;
  }
};

export const getCurrentUser = () => {
  try {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  } catch (error) {
    console.error('خطأ في الحصول على بيانات المستخدم:', error);
    return null;
  }
}; 