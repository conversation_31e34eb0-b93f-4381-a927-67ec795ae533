import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Gamepad2, 
  Trophy, 
  Users, 
  Palette, 
  Settings,
  BarChart3
} from 'lucide-react';

const Sidebar = () => {
  const location = useLocation();

  const menuItems = [
    {
      path: '/dashboard',
      name: 'الرئيسية',
      icon: Home
    },
    {
      path: '/sessions',
      name: 'الجلسات',
      icon: Gamepad2
    },
    {
      path: '/winners',
      name: 'الفائزين',
      icon: Trophy
    },
    {
      path: '/users',
      name: 'المستخدمين',
      icon: Users
    },
    {
      path: '/themes',
      name: 'المظهر',
      icon: Palette
    },
    {
      path: '/settings',
      name: 'الإعدادات',
      icon: Settings
    }
  ];

  return (
    <div className="w-64 bg-white shadow-lg min-h-screen">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <BarChart3 className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-800">لوحة التحكم</h1>
            <p className="text-sm text-gray-500">نظام الكازينو</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            
            return (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={`nav-link flex items-center gap-3 w-full ${
                    isActive ? 'active' : 'text-gray-600 hover:text-blue-600'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{item.name}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="absolute bottom-0 w-64 p-4 border-t border-gray-200">
        <div className="text-center">
          <p className="text-sm text-gray-500">نظام إدارة جلسات الكازينو</p>
          <p className="text-xs text-gray-400 mt-1">الإصدار 1.0.0</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar; 