version: '3.8'

services:
  # الباك إند
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DISCORD_TOKEN=${DISCORD_TOKEN}
      - DISCORD_CLIENT_ID=${DISCORD_CLIENT_ID}
      - DISCO<PERSON>_CLIENT_SECRET=${DISCORD_CLIENT_SECRET}
      - DISCORD_GUILD_ID=${DISCORD_GUILD_ID}
      - DATABASE_URL=sqlite:///./database/casino.db
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - SECRET_KEY=${SECRET_KEY}
      - FRONTEND_URL=http://localhost:3000
      - OVERLAY_URL=http://localhost:3001
      - REDIRECT_URI=http://localhost:8000/auth/callback
      - ADMIN_ROLE_NAME=${ADMIN_ROLE_NAME}
    volumes:
      - ./database:/app/database
    depends_on:
      - database

  # قاعدة البيانات
  database:
    image: postgres:15
    environment:
      - POSTGRES_DB=casino_db
      - POSTGRES_USER=casino_user
      - POSTGRES_PASSWORD=casino_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  # لوحة التحكم
  dashboard:
    build:
      context: ./frontend/dashboard
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend

  # الـOverlay
  overlay:
    build:
      context: ./frontend/overlay
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend

volumes:
  postgres_data: 