/* Bootstrap CSS */
@import 'bootstrap/dist/css/bootstrap.min.css';
@import 'bootstrap-icons/font/bootstrap-icons.css';

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Bootstrap Theme Colors - Dynamic */
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;

  /* Custom Theme Colors */
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --accent-color: #0dcaf0;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;

  /* Text Colors */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-tertiary: #adb5bd;

  /* Border Colors */
  --border-primary: #dee2e6;
  --border-secondary: #e9ecef;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Arial', sans-serif;
  direction: rtl;
  background-color: var(--bs-light);
  color: var(--bs-dark);
  transition: all 0.3s ease;
}

#root {
  min-height: 100vh;
}

/* Bootstrap RTL Support */
.dropdown-menu-end {
  --bs-position: start;
}

/* تأثيرات عامة */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.button-hover {
  transition: all 0.3s ease;
}

.button-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* تأثيرات النص */
.text-glow {
  text-shadow: 0 0 10px currentColor;
}

/* تأثيرات التحميل */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* تأثيرات البطاقات */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* تأثيرات الألوان */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* تأثيرات الجداول */
.table-row-hover {
  transition: background-color 0.2s ease;
}

.table-row-hover:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

/* تأثيرات الأزرار */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(240, 147, 251, 0.4);
}

/* تأثيرات التنبيهات */
.alert-success {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
}

.alert-error {
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
  color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
}

.alert-warning {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
}

/* Bootstrap Custom Styles */
.bg-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.hover-bg-light:hover {
  background-color: #f8f9fa !important;
  color: #495057 !important;
}

/* Card hover effects */
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease;
}

/* Theme-aware utility classes */
.theme-primary {
  background-color: var(--primary-color) !important;
}

.theme-secondary {
  background-color: var(--secondary-color) !important;
}

.theme-primary-text {
  color: var(--primary-color) !important;
}

.theme-secondary-text {
  color: var(--secondary-color) !important;
}

.theme-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.theme-border {
  border-color: var(--primary-color) !important;
}

/* Icon Set Styles */
.icon-set-default .theme-icon {
  filter: none;
}

.icon-set-modern .theme-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.icon-set-gaming .theme-icon {
  filter: drop-shadow(0 0 8px var(--primary-color));
}

.icon-set-neon .theme-icon {
  filter: drop-shadow(0 0 12px var(--primary-color)) brightness(1.2);
  animation: neon-glow 2s ease-in-out infinite alternate;
}

@keyframes neon-glow {
  from {
    filter: drop-shadow(0 0 12px var(--primary-color)) brightness(1.2);
  }
  to {
    filter: drop-shadow(0 0 20px var(--primary-color)) brightness(1.4);
  }
}

/* Font Classes */
.font-cairo {
  font-family: 'Cairo', sans-serif !important;
}

.font-roboto {
  font-family: 'Roboto', sans-serif !important;
}

.font-poppins {
  font-family: 'Poppins', sans-serif !important;
}

.font-inter {
  font-family: 'Inter', sans-serif !important;
}

/* Theme Transitions */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Custom Scrollbar with Theme Colors */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* تأثيرات الرسوم البيانية */
.chart-container {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* تأثيرات القوائم */
.list-item {
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.list-item:hover {
  background-color: rgba(59, 130, 246, 0.1);
  transform: translateX(-5px);
}

/* تأثيرات النماذج */
.form-input {
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 0.75rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* تأثيرات التنقل */
.nav-link {
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
}

.nav-link:hover {
  background-color: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.nav-link.active {
  background-color: #667eea;
  color: white;
}

/* تأثيرات التمرير */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}