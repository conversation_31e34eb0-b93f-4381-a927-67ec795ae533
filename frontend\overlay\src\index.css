/* Bootstrap CSS */
@import 'bootstrap/dist/css/bootstrap.min.css';
@import 'bootstrap-icons/font/bootstrap-icons.css';

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Default Theme Colors */
  --primary-color: #FF6B6B;
  --secondary-color: #4ECDC4;
  --accent-color: #45B7D1;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);

  /* Background Colors */
  --bg-overlay: rgba(0, 0, 0, 0.8);
  --bg-card: rgba(255, 255, 255, 0.1);
  --bg-glass: rgba(255, 255, 255, 0.05);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Arial', sans-serif;
  background: transparent;
  overflow: hidden;
  direction: rtl;
  transition: font-family 0.3s ease;
}

#root {
  background: transparent;
  width: 100vw;
  height: 100vh;
}

/* تأثيرات بصرية */
.glow {
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* تأثيرات خاصة للألعاب */
.slot-spin {
  animation: slotSpin 1s ease-in-out;
}

@keyframes slotSpin {
  0% {
    transform: translateY(-100px) rotate(0deg);
  }
  50% {
    transform: translateY(0) rotate(180deg);
  }
  100% {
    transform: translateY(0) rotate(360deg);
  }
}

.wheel-spin {
  animation: wheelSpin 2s ease-out;
}

@keyframes wheelSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(720deg);
  }
}

.roulette-flash {
  animation: rouletteFlash 0.5s ease-in-out;
}

@keyframes rouletteFlash {
  0%, 100% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(255, 215, 0, 0.3);
  }
}

/* تأثير الفائز */
.winner-celebration {
  animation: winnerCelebration 1s ease-in-out;
}

@keyframes winnerCelebration {
  0% {
    transform: scale(0.5) rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(360deg);
    opacity: 1;
  }
}

/* تأثيرات النص */
.text-glow {
  text-shadow: 0 0 10px currentColor;
}

.text-bounce {
  animation: textBounce 0.6s ease-in-out;
}

@keyframes textBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* تحسين الأداء */
.overlay-container {
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* تأثيرات الخلفية */
.gradient-bg {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.glass-effect {
  background: var(--bg-card);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Theme-aware utility classes */
.theme-primary {
  background-color: var(--primary-color) !important;
}

.theme-secondary {
  background-color: var(--secondary-color) !important;
}

.theme-primary-text {
  color: var(--primary-color) !important;
}

.theme-secondary-text {
  color: var(--secondary-color) !important;
}

.theme-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.theme-border {
  border-color: var(--primary-color) !important;
}

.theme-glow {
  box-shadow: 0 0 20px var(--primary-color);
}

.theme-text-glow {
  text-shadow: 0 0 10px var(--primary-color);
}

/* Icon Set Styles */
.icon-set-default .theme-icon {
  filter: none;
}

.icon-set-modern .theme-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.icon-set-gaming .theme-icon {
  filter: drop-shadow(0 0 8px var(--primary-color));
  animation: gaming-pulse 2s ease-in-out infinite;
}

.icon-set-neon .theme-icon {
  filter: drop-shadow(0 0 12px var(--primary-color)) brightness(1.3);
  animation: neon-glow 1.5s ease-in-out infinite alternate;
}

@keyframes gaming-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 8px var(--primary-color));
  }
  50% {
    filter: drop-shadow(0 0 16px var(--primary-color)) brightness(1.2);
  }
}

@keyframes neon-glow {
  from {
    filter: drop-shadow(0 0 12px var(--primary-color)) brightness(1.3);
  }
  to {
    filter: drop-shadow(0 0 20px var(--primary-color)) brightness(1.5);
  }
}

/* Font Classes */
.font-cairo {
  font-family: 'Cairo', sans-serif !important;
}

.font-roboto {
  font-family: 'Roboto', sans-serif !important;
}

.font-poppins {
  font-family: 'Poppins', sans-serif !important;
}

.font-inter {
  font-family: 'Inter', sans-serif !important;
}

/* تأثيرات الأزرار */
.button-hover {
  transition: all 0.3s ease;
}

.button-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* تأثيرات القوائم */
.list-item {
  transition: all 0.2s ease;
}

.list-item:hover {
  transform: translateX(-5px);
  background: rgba(255, 255, 255, 0.1);
}

/* تأثيرات الأيقونات */
.icon-spin {
  animation: iconSpin 1s linear infinite;
}

@keyframes iconSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* تأثيرات العدادات */
.counter {
  animation: counterPulse 0.5s ease-in-out;
}

@keyframes counterPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}