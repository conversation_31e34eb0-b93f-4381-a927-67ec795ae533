import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Palette, Paintbrush, RefreshCw } from 'lucide-react';
import toast from 'react-hot-toast';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const ThemeTest = () => {
  const [currentTheme, setCurrentTheme] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [testColors, setTestColors] = useState({
    primary: '#FF6B6B',
    secondary: '#4ECDC4'
  });

  useEffect(() => {
    fetchCurrentTheme();
  }, []);

  const fetchCurrentTheme = async () => {
    try {
      const response = await fetch(`${API_URL}/api/themes/current`);
      if (response.ok) {
        const theme = await response.json();
        setCurrentTheme(theme);
        setTestColors({
          primary: theme.primary_color,
          secondary: theme.secondary_color
        });
        
        // Apply theme to DOM
        document.documentElement.style.setProperty('--primary-color', theme.primary_color);
        document.documentElement.style.setProperty('--secondary-color', theme.secondary_color);
        document.body.style.fontFamily = theme.font;
        
        toast.success('تم تحميل السمة بنجاح');
      } else {
        toast.error('فشل في تحميل السمة');
      }
    } catch (error) {
      console.error('خطأ في تحميل السمة:', error);
      toast.error('خطأ في الاتصال بالخادم');
    } finally {
      setIsLoading(false);
    }
  };

  const testApplyTheme = async () => {
    try {
      const response = await fetch(`${API_URL}/api/themes/apply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          primary_color: testColors.primary,
          secondary_color: testColors.secondary,
          font: 'Cairo',
          icon_set: 'default'
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setCurrentTheme(result.theme);
        
        // Apply to DOM
        document.documentElement.style.setProperty('--primary-color', testColors.primary);
        document.documentElement.style.setProperty('--secondary-color', testColors.secondary);
        
        toast.success('تم تطبيق السمة بنجاح');
      } else {
        toast.error('فشل في تطبيق السمة');
      }
    } catch (error) {
      console.error('خطأ في تطبيق السمة:', error);
      toast.error('خطأ في الاتصال بالخادم');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="flex items-center gap-3 mb-8">
        <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
          <Palette className="w-6 h-6 text-white" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">اختبار نظام السمات</h1>
          <p className="text-gray-600">اختبار وظائف السمات الأساسية</p>
        </div>
      </div>

      {/* Current Theme Display */}
      {currentTheme && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">السمة الحالية</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اللون الأساسي
              </label>
              <div className="flex items-center gap-2">
                <div 
                  className="w-8 h-8 rounded border border-gray-300"
                  style={{ backgroundColor: currentTheme.primary_color }}
                ></div>
                <span className="text-sm">{currentTheme.primary_color}</span>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اللون الثانوي
              </label>
              <div className="flex items-center gap-2">
                <div 
                  className="w-8 h-8 rounded border border-gray-300"
                  style={{ backgroundColor: currentTheme.secondary_color }}
                ></div>
                <span className="text-sm">{currentTheme.secondary_color}</span>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الخط
              </label>
              <span className="text-sm">{currentTheme.font}</span>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الأيقونات
              </label>
              <span className="text-sm">{currentTheme.icon_set}</span>
            </div>
          </div>
        </motion.div>
      )}

      {/* Test Color Picker */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">اختبار تغيير الألوان</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اللون الأساسي
            </label>
            <div className="flex items-center gap-3">
              <input
                type="color"
                value={testColors.primary}
                onChange={(e) => setTestColors({...testColors, primary: e.target.value})}
                className="w-12 h-12 rounded-lg border border-gray-300 cursor-pointer"
              />
              <input
                type="text"
                value={testColors.primary}
                onChange={(e) => setTestColors({...testColors, primary: e.target.value})}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اللون الثانوي
            </label>
            <div className="flex items-center gap-3">
              <input
                type="color"
                value={testColors.secondary}
                onChange={(e) => setTestColors({...testColors, secondary: e.target.value})}
                className="w-12 h-12 rounded-lg border border-gray-300 cursor-pointer"
              />
              <input
                type="text"
                value={testColors.secondary}
                onChange={(e) => setTestColors({...testColors, secondary: e.target.value})}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <button
            onClick={testApplyTheme}
            className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all flex items-center gap-2"
          >
            <Paintbrush className="w-4 h-4" />
            تطبيق الألوان
          </button>
        </div>
      </div>

      {/* Theme Preview */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">معاينة السمة</h3>
        
        {/* Sample UI with current theme */}
        <div 
          className="p-6 rounded-xl text-white mb-4"
          style={{ 
            background: `linear-gradient(135deg, ${testColors.primary}, ${testColors.secondary})`,
            fontFamily: currentTheme?.font || 'Cairo'
          }}
        >
          <h4 className="text-2xl font-bold mb-2">عنوان تجريبي</h4>
          <p className="text-lg opacity-90 mb-4">هذا نص تجريبي لمعاينة السمة الحالية</p>
          
          <div className="flex gap-3">
            <button className="px-4 py-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-all">
              زر تجريبي
            </button>
            <button className="px-4 py-2 bg-black bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-all">
              زر آخر
            </button>
          </div>
        </div>

        <div className="flex justify-center">
          <button
            onClick={fetchCurrentTheme}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            إعادة تحميل السمة
          </button>
        </div>
      </div>
    </div>
  );
};

export default ThemeTest;
