import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { motion } from 'framer-motion';

// مكونات لوحة التحكم
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Dashboard from './pages/Dashboard';
import Sessions from './pages/Sessions';
import Winners from './pages/Winners';
import Users from './pages/Users';
import Themes from './pages/Themes';
import Settings from './pages/Settings';
import Login from './pages/Login';
import ThemeTest from './pages/ThemeTest';

// خدمات API
import { checkAuthStatus } from './services/auth';

// Theme Context
import { ThemeProvider } from './contexts/ThemeContext';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// React Router future flags to suppress warnings
const router = {
  future: {
    v7_startTransition: true,
    v7_relativeSplatPath: true
  }
};

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);

  useEffect(() => {
    checkAuthStatus()
      .then((authData) => {
        if (authData.isAuthenticated) {
          setIsAuthenticated(true);
          setUser(authData.user);
        }
      })
      .catch((error) => {
        console.error('خطأ في التحقق من حالة المصادقة:', error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="loading-spinner w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <Login onLogin={(userData) => {
          setIsAuthenticated(true);
          setUser(userData);
        }} />
        <Toaster position="top-center" />
      </div>
    );
  }

  return (
    <ThemeProvider>
      <Router {...router}>
        <div className="min-h-screen bg-gray-50 theme-transition">
        <div className="flex">
          {/* Sidebar */}
          <Sidebar />

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            <Header user={user} onLogout={() => setIsAuthenticated(false)} />

            <main className="flex-1 p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/sessions" element={<Sessions />} />
                  <Route path="/winners" element={<Winners />} />
                  <Route path="/users" element={<Users />} />
                  <Route path="/themes" element={<Themes />} />
                  <Route path="/theme-test" element={<ThemeTest />} />
                  <Route path="/settings" element={<Settings />} />
                </Routes>
              </motion.div>
            </main>
          </div>
        </div>

        <Toaster
          position="top-center"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
              fontFamily: 'Cairo, Arial, sans-serif',
            },
            success: {
              duration: 3000,
              iconTheme: {
                primary: '#4ade80',
                secondary: '#fff',
              },
            },
            error: {
              duration: 5000,
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;