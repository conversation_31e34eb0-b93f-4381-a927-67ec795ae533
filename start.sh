#!/bin/bash

echo "========================================"
echo "   نظام إدارة جلسات الكازينو"
echo "========================================"
echo

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ خطأ: Python3 غير مثبت"
    exit 1
fi

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ خطأ: Node.js غير مثبت"
    exit 1
fi

echo "✅ تم التحقق من المتطلبات الأساسية"
echo

# إنشاء البيئة الافتراضية إذا لم تكن موجودة
if [ ! -d "venv" ]; then
    echo "📦 إنشاء البيئة الافتراضية..."
    python3 -m venv venv
fi

# تفعيل البيئة الافتراضية
echo "🔄 تفعيل البيئة الافتراضية..."
source venv/bin/activate

# تثبيت متطلبات Python
echo "📥 تثبيت متطلبات Python..."
pip install -r requirements.txt

# إنشاء مجلد قاعدة البيانات
mkdir -p database

echo
echo "🚀 بدء تشغيل النظام..."
echo

# تشغيل الباك إند في الخلفية
echo "🔧 تشغيل الباك إند..."
python main.py &
BACKEND_PID=$!

# انتظار قليل
sleep 3

# تشغيل الـOverlay
if [ -d "frontend/overlay" ]; then
    echo "🎥 تشغيل الـOverlay..."
    cd frontend/overlay
    npm install
    npm start &
    OVERLAY_PID=$!
    cd ../..
fi

# تشغيل لوحة التحكم
if [ -d "frontend/dashboard" ]; then
    echo "🛠️ تشغيل لوحة التحكم..."
    cd frontend/dashboard
    npm install
    npm start &
    DASHBOARD_PID=$!
    cd ../..
fi

echo
echo "✅ تم تشغيل جميع الخدمات!"
echo
echo "🌐 الروابط المتاحة:"
echo "   الباك إند API: http://localhost:8000"
echo "   لوحة التحكم: http://localhost:3000"
echo "   الـOverlay: http://localhost:3001"
echo
echo "📖 للمزيد من المعلومات، راجع docs/SETUP.md"
echo

# دالة تنظيف عند الإغلاق
cleanup() {
    echo
    echo "🛑 إغلاق النظام..."
    kill $BACKEND_PID 2>/dev/null
    kill $OVERLAY_PID 2>/dev/null
    kill $DASHBOARD_PID 2>/dev/null
    exit 0
}

# التقاط إشارة الإغلاق
trap cleanup SIGINT SIGTERM

# انتظار المستخدم
echo "اضغط Ctrl+C لإغلاق النظام"
wait 