import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Calendar, Clock, Users, DollarSign, Play, Pause, StopCircle } from 'lucide-react';
import toast from 'react-hot-toast';

const Sessions = () => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    // Simulate loading sessions data
    setTimeout(() => {
      setSessions([
        {
          id: 1,
          name: 'جلسة الصباح',
          status: 'active',
          startTime: '2024-01-15T08:00:00',
          endTime: null,
          players: 45,
          totalBets: 12500,
          totalWins: 8900,
          theme: 'classic'
        },
        {
          id: 2,
          name: 'جلسة المساء',
          status: 'paused',
          startTime: '2024-01-15T18:00:00',
          endTime: null,
          players: 32,
          totalBets: 8900,
          totalWins: 6700,
          theme: 'modern'
        },
        {
          id: 3,
          name: 'جلسة الليل',
          status: 'ended',
          startTime: '2024-01-14T22:00:00',
          endTime: '2024-01-15T06:00:00',
          players: 28,
          totalBets: 15600,
          totalWins: 12300,
          theme: 'dark'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const handleSessionAction = (sessionId, action) => {
    setSessions(prev => prev.map(session => {
      if (session.id === sessionId) {
        return { ...session, status: action };
      }
      return session;
    }));
    toast.success(`تم ${action === 'active' ? 'تشغيل' : action === 'paused' ? 'إيقاف مؤقت' : 'إيقاف'} الجلسة بنجاح`);
  };

  const filteredSessions = sessions.filter(session => {
    if (filter === 'all') return true;
    return session.status === filter;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'paused': return 'text-yellow-600 bg-yellow-100';
      case 'ended': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'paused': return 'متوقف مؤقت';
      case 'ended': return 'منتهي';
      default: return 'غير معروف';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الجلسات</h1>
          <p className="text-gray-600 mt-1">إدارة جلسات الألعاب والمراقبة</p>
        </div>
        <button className="mt-4 sm:mt-0 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
          إنشاء جلسة جديدة
        </button>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-2">
        {['all', 'active', 'paused', 'ended'].map((filterOption) => (
          <button
            key={filterOption}
            onClick={() => setFilter(filterOption)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === filterOption
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {filterOption === 'all' ? 'الكل' : 
             filterOption === 'active' ? 'نشط' :
             filterOption === 'paused' ? 'متوقف مؤقت' : 'منتهي'}
          </button>
        ))}
      </div>

      {/* Sessions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSessions.map((session) => (
          <motion.div
            key={session.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2 }}
            className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
          >
            {/* Session Header */}
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{session.name}</h3>
                <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                  {getStatusText(session.status)}
                </span>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">السمة</p>
                <p className="text-sm font-medium text-gray-900">{session.theme}</p>
              </div>
            </div>

            {/* Session Stats */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Users className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">اللاعبين</p>
                  <p className="font-semibold text-gray-900">{session.players}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <DollarSign className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">إجمالي الرهانات</p>
                  <p className="font-semibold text-gray-900">${session.totalBets.toLocaleString()}</p>
                </div>
              </div>
            </div>

            {/* Session Time */}
            <div className="flex items-center space-x-2 space-x-reverse mb-4">
              <Calendar className="w-4 h-4 text-gray-400" />
              <div className="text-sm text-gray-600">
                {new Date(session.startTime).toLocaleDateString('ar-SA')}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              {session.status === 'paused' && (
                <button
                  onClick={() => handleSessionAction(session.id, 'active')}
                  className="flex-1 bg-green-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-green-700 transition-colors flex items-center justify-center space-x-1 space-x-reverse"
                >
                  <Play className="w-4 h-4" />
                  <span>تشغيل</span>
                </button>
              )}
              {session.status === 'active' && (
                <button
                  onClick={() => handleSessionAction(session.id, 'paused')}
                  className="flex-1 bg-yellow-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-yellow-700 transition-colors flex items-center justify-center space-x-1 space-x-reverse"
                >
                  <Pause className="w-4 h-4" />
                  <span>إيقاف مؤقت</span>
                </button>
              )}
              {session.status !== 'ended' && (
                <button
                  onClick={() => handleSessionAction(session.id, 'ended')}
                  className="flex-1 bg-red-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-red-700 transition-colors flex items-center justify-center space-x-1 space-x-reverse"
                >
                  <StopCircle className="w-4 h-4" />
                  <span>إنهاء</span>
                </button>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {filteredSessions.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">لا توجد جلسات متاحة</p>
        </div>
      )}
    </motion.div>
  );
};

export default Sessions; 