import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, Medal, Star, TrendingUp, Users } from 'lucide-react';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const Leaderboard = ({ theme }) => {
  const [topPlayers, setTopPlayers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showFullList, setShowFullList] = useState(false);

  useEffect(() => {
    fetchTopPlayers();
    
    // تحديث كل 30 ثانية
    const interval = setInterval(fetchTopPlayers, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchTopPlayers = async () => {
    try {
      const response = await fetch(`${API_URL}/api/stats/overview`);
      if (response.ok) {
        const data = await response.json();
        setTopPlayers(data.top_players || []);
      }
    } catch (error) {
      console.error('خطأ في تحميل الليدر بورد:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getMedalIcon = (position) => {
    switch (position) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return `${position}`;
    }
  };

  const getMedalColor = (position) => {
    switch (position) {
      case 1:
        return 'from-yellow-400 to-yellow-600';
      case 2:
        return 'from-gray-300 to-gray-500';
      case 3:
        return 'from-orange-400 to-orange-600';
      default:
        return 'from-blue-400 to-blue-600';
    }
  };

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="glass-effect rounded-xl p-4 min-w-[280px]"
        style={{ borderColor: theme.secondary_color }}
      >
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white text-sm">جاري التحميل...</p>
        </div>
      </motion.div>
    );
  }

  const displayPlayers = showFullList ? topPlayers : topPlayers.slice(0, 5);

  return (
    <motion.div
      initial={{ opacity: 0, x: -100 }}
      animate={{ opacity: 1, x: 0 }}
      className="glass-effect rounded-xl p-4 min-w-[280px]"
      style={{
        borderColor: theme.secondary_color,
        maxHeight: showFullList ? '70vh' : '400px',
        overflow: 'hidden'
      }}
    >
      {/* العنوان */}
      <div className="flex items-center gap-2 mb-4">
        <Trophy size={20} className="text-white" />
        <h3 className="text-lg font-bold text-white">أفضل اللاعبين</h3>
        <TrendingUp size={16} className="text-green-400" />
      </div>

      {/* قائمة اللاعبين */}
      <div className="space-y-2 max-h-[300px] overflow-y-auto">
        <AnimatePresence>
          {displayPlayers.map((player, index) => (
            <motion.div
              key={player.username}
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 50 }}
              transition={{ delay: index * 0.1 }}
              className="list-item flex items-center justify-between p-3 rounded-lg bg-white bg-opacity-10 hover:bg-opacity-20 transition-all"
            >
              {/* الترتيب والاسم */}
              <div className="flex items-center gap-3">
                <div className={`w-8 h-8 rounded-full bg-gradient-to-r ${getMedalColor(index + 1)} flex items-center justify-center text-white font-bold text-sm`}>
                  {getMedalIcon(index + 1)}
                </div>
                <div>
                  <p className="text-white font-medium text-sm">
                    {player.username}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-gray-300">
                    <Star size={10} />
                    <span>{player.total_wins} فوز</span>
                  </div>
                </div>
              </div>

              {/* الإحصائيات */}
              <div className="text-right">
                <div className="text-white font-bold text-sm">
                  {player.points} نقطة
                </div>
                <div className="text-xs text-gray-300">
                  {player.total_participations} مشاركة
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* زر إظهار المزيد */}
      {topPlayers.length > 5 && (
        <motion.button
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          onClick={() => setShowFullList(!showFullList)}
          className="w-full mt-3 py-2 text-sm text-white bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-all button-hover"
        >
          {showFullList ? 'إظهار أقل' : `إظهار المزيد (${topPlayers.length - 5})`}
        </motion.button>
      )}

      {/* رسالة عند عدم وجود لاعبين */}
      {topPlayers.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-8"
        >
          <Users size={32} className="mx-auto mb-2 text-gray-400" />
          <p className="text-gray-400 text-sm">لا يوجد لاعبين بعد</p>
        </motion.div>
      )}

      {/* إحصائيات سريعة */}
      {topPlayers.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4 pt-4 border-t border-white border-opacity-20"
        >
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-white font-bold text-lg">
                {topPlayers[0]?.total_wins || 0}
              </div>
              <div className="text-xs text-gray-300">أعلى فوز</div>
            </div>
            <div>
              <div className="text-white font-bold text-lg">
                {topPlayers[0]?.points || 0}
              </div>
              <div className="text-xs text-gray-300">أعلى نقاط</div>
            </div>
          </div>
        </motion.div>
      )}

      {/* تأثيرات بصرية */}
      <motion.div
        animate={{ 
          boxShadow: [
            `0 0 10px ${theme.secondary_color}20`,
            `0 0 20px ${theme.secondary_color}40`,
            `0 0 10px ${theme.secondary_color}20`
          ]
        }}
        transition={{ duration: 3, repeat: Infinity }}
        className="absolute inset-0 rounded-xl pointer-events-none"
      />
    </motion.div>
  );
};

export default Leaderboard; 