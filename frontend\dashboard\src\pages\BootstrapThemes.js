import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Palette, 
  Plus, 
  Save, 
  Trash2, 
  Eye, 
  Download,
  Upload,
  Paintbrush,
  Sparkles
} from 'lucide-react';
import toast from 'react-hot-toast';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const BootstrapThemes = () => {
  const [themes, setThemes] = useState([]);
  const [currentTheme, setCurrentTheme] = useState(null);
  const [selectedTheme, setSelectedTheme] = useState(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [previewMode, setPreviewMode] = useState(false);

  const [newTheme, setNewTheme] = useState({
    name: '',
    primary_color: '#0d6efd',
    secondary_color: '#6c757d',
    font: 'Cairo',
    icon_set: 'default'
  });

  const fontOptions = [
    { value: 'Cairo', label: 'Cairo - القاهرة' },
    { value: 'Arial', label: 'Arial' },
    { value: 'Roboto', label: 'Roboto' },
    { value: 'Poppins', label: 'Poppins' },
    { value: 'Inter', label: 'Inter' }
  ];

  const iconSetOptions = [
    { value: 'default', label: 'افتراضي', description: 'أيقونات Bootstrap' },
    { value: 'modern', label: 'عصري', description: 'أيقونات حديثة' },
    { value: 'gaming', label: 'ألعاب', description: 'أيقونات الألعاب' },
    { value: 'neon', label: 'نيون', description: 'أيقونات متوهجة' }
  ];

  useEffect(() => {
    fetchThemes();
    fetchCurrentTheme();
  }, []);

  const fetchThemes = async () => {
    try {
      const response = await fetch(`${API_URL}/api/themes`);
      if (response.ok) {
        const data = await response.json();
        setThemes(data.themes || []);
      }
    } catch (error) {
      toast.error('فشل في تحميل السمات');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCurrentTheme = async () => {
    try {
      const response = await fetch(`${API_URL}/api/themes/current`);
      if (response.ok) {
        const theme = await response.json();
        setCurrentTheme(theme);
        applyThemeToDOM(theme);
      }
    } catch (error) {
      console.error('خطأ في جلب السمة الحالية:', error);
    }
  };

  const applyThemeToDOM = (theme) => {
    document.documentElement.style.setProperty('--bs-primary', theme.primary_color);
    document.documentElement.style.setProperty('--bs-secondary', theme.secondary_color);
    document.documentElement.style.setProperty('--primary-color', theme.primary_color);
    document.documentElement.style.setProperty('--secondary-color', theme.secondary_color);
    document.body.style.fontFamily = theme.font;
  };

  const createTheme = async () => {
    if (!newTheme.name.trim()) {
      toast.error('يرجى إدخال اسم السمة');
      return;
    }

    try {
      const response = await fetch(`${API_URL}/api/themes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newTheme),
      });

      if (response.ok) {
        toast.success('تم إنشاء السمة بنجاح');
        setIsCreating(false);
        setNewTheme({
          name: '',
          primary_color: '#0d6efd',
          secondary_color: '#6c757d',
          font: 'Cairo',
          icon_set: 'default'
        });
        fetchThemes();
      } else {
        const error = await response.json();
        toast.error(error.detail || 'فشل في إنشاء السمة');
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء إنشاء السمة');
    }
  };

  const applyTheme = async (theme) => {
    try {
      const response = await fetch(`${API_URL}/api/themes/apply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          primary_color: theme.primary_color,
          secondary_color: theme.secondary_color,
          font: theme.font,
          icon_set: theme.icon_set
        }),
      });

      if (response.ok) {
        toast.success('تم تطبيق السمة بنجاح');
        setCurrentTheme(theme);
        applyThemeToDOM(theme);
      } else {
        toast.error('فشل في تطبيق السمة');
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء تطبيق السمة');
    }
  };

  const deleteTheme = async (themeId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه السمة؟')) {
      return;
    }

    try {
      const response = await fetch(`${API_URL}/api/themes/${themeId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('تم حذف السمة بنجاح');
        fetchThemes();
      } else {
        toast.error('فشل في حذف السمة');
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء حذف السمة');
    }
  };

  if (isLoading) {
    return (
      <div className="d-flex align-items-center justify-content-center" style={{height: '16rem'}}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="d-flex align-items-center justify-content-between mb-4">
        <div className="d-flex align-items-center">
          <div className="bg-gradient text-white rounded p-2 me-3">
            <Palette size={24} />
          </div>
          <div>
            <h1 className="h2 fw-bold text-dark mb-1">إدارة السمات</h1>
            <p className="text-muted mb-0">تخصيص مظهر الموقع بـ Bootstrap</p>
          </div>
        </div>
        
        <div className="d-flex gap-2">
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className={`btn ${previewMode ? 'btn-primary' : 'btn-outline-primary'}`}
          >
            <Eye size={16} className="me-1" />
            {previewMode ? 'إخفاء المعاينة' : 'معاينة'}
          </button>
          
          <button
            onClick={() => setIsCreating(true)}
            className="btn btn-primary"
          >
            <Plus size={16} className="me-1" />
            سمة جديدة
          </button>
        </div>
      </div>

      {/* Current Theme Display */}
      {currentTheme && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="card border-primary mb-4"
        >
          <div className="card-body bg-primary bg-opacity-10">
            <div className="d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center">
                <div className="d-flex gap-2 me-3">
                  <div 
                    className="rounded-circle border border-white shadow-sm"
                    style={{ 
                      backgroundColor: currentTheme.primary_color,
                      width: '32px',
                      height: '32px'
                    }}
                  ></div>
                  <div 
                    className="rounded-circle border border-white shadow-sm"
                    style={{ 
                      backgroundColor: currentTheme.secondary_color,
                      width: '32px',
                      height: '32px'
                    }}
                  ></div>
                </div>
                <div>
                  <h3 className="h5 fw-semibold text-dark mb-1">السمة الحالية</h3>
                  <p className="text-muted mb-0">
                    الخط: {currentTheme.font} | الأيقونات: {currentTheme.icon_set}
                  </p>
                </div>
              </div>
              <div className="d-flex align-items-center text-success">
                <div className="bg-success rounded-circle me-2" style={{width: '8px', height: '8px'}}></div>
                <span className="small fw-medium">نشط</span>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Themes Grid */}
      <div className="row g-4">
        {themes.map((theme) => {
          const isActive = currentTheme && 
            currentTheme.primary_color === theme.primary_color && 
            currentTheme.secondary_color === theme.secondary_color;

          return (
            <div key={theme.id} className="col-md-6 col-lg-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`card h-100 ${isActive ? 'border-success' : ''}`}
              >
                {/* Theme Preview */}
                <div 
                  className="card-img-top position-relative d-flex align-items-center justify-content-center text-white"
                  style={{ 
                    height: '120px',
                    background: `linear-gradient(135deg, ${theme.primary_color}, ${theme.secondary_color})`,
                    fontFamily: theme.font
                  }}
                >
                  <div className="text-center">
                    <h5 className="fw-bold mb-1">{theme.name}</h5>
                    <small className="opacity-75">معاينة السمة</small>
                  </div>
                  
                  {isActive && (
                    <span className="position-absolute top-0 end-0 m-2 badge bg-success">
                      نشط
                    </span>
                  )}
                </div>

                <div className="card-body">
                  <div className="d-flex align-items-center justify-content-between mb-3">
                    <h6 className="card-title fw-semibold mb-0">{theme.name}</h6>
                    <div className="d-flex gap-1">
                      <div 
                        className="rounded border"
                        style={{ 
                          backgroundColor: theme.primary_color,
                          width: '16px',
                          height: '16px'
                        }}
                        title={`اللون الأساسي: ${theme.primary_color}`}
                      ></div>
                      <div 
                        className="rounded border"
                        style={{ 
                          backgroundColor: theme.secondary_color,
                          width: '16px',
                          height: '16px'
                        }}
                        title={`اللون الثانوي: ${theme.secondary_color}`}
                      ></div>
                    </div>
                  </div>

                  <div className="small text-muted mb-3">
                    <div>الخط: {theme.font}</div>
                    <div>الأيقونات: {theme.icon_set}</div>
                    <div>تم الإنشاء: {new Date(theme.created_at).toLocaleDateString('ar-SA')}</div>
                  </div>

                  <div className="d-flex gap-2">
                    <button
                      onClick={() => applyTheme(theme)}
                      className="btn btn-primary btn-sm flex-fill"
                    >
                      <Paintbrush size={14} className="me-1" />
                      تطبيق
                    </button>
                    
                    <button
                      onClick={() => setSelectedTheme(theme)}
                      className="btn btn-outline-secondary btn-sm"
                      title="عرض التفاصيل"
                    >
                      <Eye size={14} />
                    </button>
                    
                    <button
                      onClick={() => deleteTheme(theme.id)}
                      className="btn btn-outline-danger btn-sm"
                      title="حذف السمة"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                </div>
              </motion.div>
            </div>
          );
        })}

        {/* Empty State */}
        {themes.length === 0 && (
          <div className="col-12">
            <div className="text-center py-5">
              <Palette size={64} className="text-muted mb-3" />
              <h3 className="h5 fw-medium text-dark mb-2">لا توجد سمات محفوظة</h3>
              <p className="text-muted mb-4">ابدأ بإنشاء سمة جديدة لتخصيص مظهر الموقع</p>
              <button
                onClick={() => setIsCreating(true)}
                className="btn btn-primary"
              >
                <Plus size={16} className="me-1" />
                إنشاء سمة جديدة
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Create Theme Modal */}
      {isCreating && (
        <div className="modal d-block" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
          <div className="modal-dialog modal-lg modal-dialog-centered">
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="modal-content"
            >
              <div className="modal-header">
                <h5 className="modal-title fw-bold">إنشاء سمة جديدة</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setIsCreating(false)}
                ></button>
              </div>

              <div className="modal-body">
                <div className="row g-3">
                  <div className="col-12">
                    <label className="form-label fw-medium">اسم السمة</label>
                    <input
                      type="text"
                      value={newTheme.name}
                      onChange={(e) => setNewTheme({...newTheme, name: e.target.value})}
                      className="form-control"
                      placeholder="مثال: سمة Bootstrap"
                    />
                  </div>

                  <div className="col-md-6">
                    <label className="form-label fw-medium">اللون الأساسي</label>
                    <div className="d-flex align-items-center gap-2">
                      <input
                        type="color"
                        value={newTheme.primary_color}
                        onChange={(e) => setNewTheme({...newTheme, primary_color: e.target.value})}
                        className="form-control form-control-color"
                        style={{width: '48px', height: '48px'}}
                      />
                      <input
                        type="text"
                        value={newTheme.primary_color}
                        onChange={(e) => setNewTheme({...newTheme, primary_color: e.target.value})}
                        className="form-control"
                        placeholder="#0d6efd"
                      />
                    </div>
                  </div>

                  <div className="col-md-6">
                    <label className="form-label fw-medium">اللون الثانوي</label>
                    <div className="d-flex align-items-center gap-2">
                      <input
                        type="color"
                        value={newTheme.secondary_color}
                        onChange={(e) => setNewTheme({...newTheme, secondary_color: e.target.value})}
                        className="form-control form-control-color"
                        style={{width: '48px', height: '48px'}}
                      />
                      <input
                        type="text"
                        value={newTheme.secondary_color}
                        onChange={(e) => setNewTheme({...newTheme, secondary_color: e.target.value})}
                        className="form-control"
                        placeholder="#6c757d"
                      />
                    </div>
                  </div>

                  <div className="col-md-6">
                    <label className="form-label fw-medium">نوع الخط</label>
                    <select
                      value={newTheme.font}
                      onChange={(e) => setNewTheme({...newTheme, font: e.target.value})}
                      className="form-select"
                    >
                      {fontOptions.map((font) => (
                        <option key={font.value} value={font.value}>
                          {font.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="col-md-6">
                    <label className="form-label fw-medium">مجموعة الأيقونات</label>
                    <select
                      value={newTheme.icon_set}
                      onChange={(e) => setNewTheme({...newTheme, icon_set: e.target.value})}
                      className="form-select"
                    >
                      {iconSetOptions.map((iconSet) => (
                        <option key={iconSet.value} value={iconSet.value}>
                          {iconSet.label} - {iconSet.description}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Preview */}
                  <div className="col-12">
                    <label className="form-label fw-medium">معاينة السمة</label>
                    <div 
                      className="p-4 rounded text-white"
                      style={{ 
                        background: `linear-gradient(135deg, ${newTheme.primary_color}, ${newTheme.secondary_color})`,
                        fontFamily: newTheme.font
                      }}
                    >
                      <h5 className="fw-bold mb-2">عنوان تجريبي</h5>
                      <p className="mb-0 opacity-75">هذا نص تجريبي لمعاينة السمة الجديدة</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setIsCreating(false)}
                >
                  إلغاء
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={createTheme}
                >
                  <Save size={16} className="me-1" />
                  حفظ السمة
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BootstrapThemes;
