"""
API Routes للنظام
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2Password<PERSON>earer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import List, Optional
import pandas as pd
from datetime import datetime, timedelta
import json

from backend.database.database import get_db, Session as DBSession, Participant, Winner, Theme, AdminLog, UserStats

router = APIRouter()

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# ==================== الجلسات ====================

@router.get("/sessions/current")
async def get_current_session(db: Session = Depends(get_db)):
    """الحصول على الجلسة الحالية"""
    try:
        current_session = db.query(DBSession).filter(DBSession.status.in_(['open', 'closed'])).first()
        if not current_session:
            return {"message": "لا توجد جلسة نشطة"}

        participants = db.query(Participant).filter(Participant.session_id == current_session.id).all()

        return {
            "session": {
                "id": current_session.id,
                "name": current_session.name,
                "game_type": current_session.game_type,
                "status": current_session.status,
                "created_at": current_session.created_at.isoformat(),
                "participants_count": len(participants)
            },
            "participants": [
                {
                    "user_id": p.user_id,
                    "username": p.username,
                    "joined_at": p.joined_at.isoformat()
                } for p in participants
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions/history")
async def get_sessions_history(
    limit: int = 10,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """الحصول على تاريخ الجلسات"""
    try:
        sessions = db.query(DBSession).order_by(DBSession.created_at.desc()).offset(offset).limit(limit).all()

        result = []
        for session in sessions:
            participants_count = db.query(Participant).filter(Participant.session_id == session.id).count()
            winners_count = db.query(Winner).filter(Winner.session_id == session.id).count()

            result.append({
                "id": session.id,
                "name": session.name,
                "game_type": session.game_type,
                "status": session.status,
                "created_at": session.created_at.isoformat(),
                "closed_at": session.closed_at.isoformat() if session.closed_at else None,
                "participants_count": participants_count,
                "winners_count": winners_count
            })

        return {"sessions": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ==================== المشاركين ====================

@router.get("/participants/{session_id}")
async def get_session_participants(session_id: int, db: Session = Depends(get_db)):
    """الحصول على مشاركي جلسة معينة"""
    try:
        participants = db.query(Participant).filter(Participant.session_id == session_id).all()

        return {
            "participants": [
                {
                    "user_id": p.user_id,
                    "username": p.username,
                    "joined_at": p.joined_at.isoformat()
                } for p in participants
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ==================== الفائزين ====================

@router.get("/winners")
async def get_winners(
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """الحصول على قائمة الفائزين"""
    try:
        winners = db.query(Winner).order_by(Winner.win_time.desc()).offset(offset).limit(limit).all()

        result = []
        for winner in winners:
            session = db.query(DBSession).filter(DBSession.id == winner.session_id).first()
            result.append({
                "id": winner.id,
                "user_id": winner.user_id,
                "username": winner.username,
                "win_time": winner.win_time.isoformat(),
                "session_name": session.name if session else "غير معروف",
                "game_type": session.game_type if session else "غير معروف"
            })

        return {"winners": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/winners/search")
async def search_winners(
    username: Optional[str] = None,
    game_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """البحث في الفائزين"""
    try:
        query = db.query(Winner)

        if username:
            query = query.filter(Winner.username.ilike(f"%{username}%"))

        if game_type:
            query = query.join(DBSession).filter(DBSession.game_type == game_type)

        winners = query.order_by(Winner.win_time.desc()).all()

        result = []
        for winner in winners:
            session = db.query(DBSession).filter(DBSession.id == winner.session_id).first()
            result.append({
                "id": winner.id,
                "user_id": winner.user_id,
                "username": winner.username,
                "win_time": winner.win_time.isoformat(),
                "session_name": session.name if session else "غير معروف",
                "game_type": session.game_type if session else "غير معروف"
            })

        return {"winners": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ==================== الإحصائيات ====================

@router.get("/stats/overview")
async def get_overview_stats(db: Session = Depends(get_db)):
    """الحصول على إحصائيات عامة"""
    try:
        total_sessions = db.query(DBSession).count()
        total_participants = db.query(Participant).count()
        total_winners = db.query(Winner).count()
        total_users = db.query(UserStats).count()

        # الجلسات النشطة
        active_sessions = db.query(DBSession).filter(DBSession.status.in_(['open', 'closed'])).count()

        # أكثر الألعاب لعبًا
        game_stats = db.query(DBSession.game_type, db.func.count(DBSession.id)).group_by(DBSession.game_type).all()

        # أفضل اللاعبين
        top_players = db.query(UserStats).order_by(UserStats.total_wins.desc()).limit(5).all()

        return {
            "overview": {
                "total_sessions": total_sessions,
                "active_sessions": active_sessions,
                "total_participants": total_participants,
                "total_winners": total_winners,
                "total_users": total_users
            },
            "game_stats": [
                {"game_type": game_type, "count": count} for game_type, count in game_stats
            ],
            "top_players": [
                {
                    "username": player.username,
                    "total_wins": player.total_wins,
                    "total_participations": player.total_participations,
                    "points": player.points
                } for player in top_players
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats/user/{user_id}")
async def get_user_stats(user_id: str, db: Session = Depends(get_db)):
    """الحصول على إحصائيات مستخدم معين"""
    try:
        user_stats = db.query(UserStats).filter(UserStats.user_id == user_id).first()
        if not user_stats:
            raise HTTPException(status_code=404, detail="المستخدم غير موجود")

        # الفوائز الأخيرة
        recent_wins = db.query(Winner).filter(Winner.user_id == user_id).order_by(Winner.win_time.desc()).limit(5).all()

        # المشاركات الأخيرة
        recent_participations = db.query(Participant).filter(Participant.user_id == user_id).order_by(Participant.joined_at.desc()).limit(5).all()

        return {
            "user_stats": {
                "username": user_stats.username,
                "total_participations": user_stats.total_participations,
                "total_wins": user_stats.total_wins,
                "points": user_stats.points,
                "last_win": user_stats.last_win.isoformat() if user_stats.last_win else None,
                "win_rate": (user_stats.total_wins / user_stats.total_participations * 100) if user_stats.total_participations > 0 else 0
            },
            "recent_wins": [
                {
                    "session_name": db.query(DBSession).filter(DBSession.id == win.session_id).first().name,
                    "win_time": win.win_time.isoformat(),
                    "game_type": db.query(DBSession).filter(DBSession.id == win.session_id).first().game_type
                } for win in recent_wins
            ],
            "recent_participations": [
                {
                    "session_name": db.query(DBSession).filter(DBSession.id == part.session_id).first().name,
                    "joined_at": part.joined_at.isoformat(),
                    "game_type": db.query(DBSession).filter(DBSession.id == part.session_id).first().game_type
                } for part in recent_participations
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ==================== Themes ====================

@router.get("/themes")
async def get_themes(db: Session = Depends(get_db)):
    """الحصول على جميع الـthemes"""
    try:
        themes = db.query(Theme).filter(Theme.name != "current").all()

        return {
            "themes": [
                {
                    "id": theme.id,
                    "name": theme.name,
                    "primary_color": theme.primary_color,
                    "secondary_color": theme.secondary_color,
                    "font": theme.font,
                    "icon_set": theme.icon_set,
                    "created_at": theme.created_at.isoformat()
                } for theme in themes
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/themes/current")
async def get_current_theme(db: Session = Depends(get_db)):
    """الحصول على الـtheme الحالي"""
    try:
        current_theme = db.query(Theme).filter(Theme.name == "current").first()
        if not current_theme:
            # إرجاع theme افتراضي
            return {
                "primary_color": "#FF6B6B",
                "secondary_color": "#4ECDC4",
                "font": "Arial",
                "icon_set": "default"
            }

        return {
            "primary_color": current_theme.primary_color,
            "secondary_color": current_theme.secondary_color,
            "font": current_theme.font,
            "icon_set": current_theme.icon_set
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/themes")
async def create_theme(theme_data: dict, db: Session = Depends(get_db)):
    """إنشاء theme جديد"""
    try:
        # التحقق من وجود theme بنفس الاسم
        existing_theme = db.query(Theme).filter(Theme.name == theme_data.get("name")).first()
        if existing_theme:
            raise HTTPException(status_code=400, detail="يوجد theme بهذا الاسم بالفعل")

        # إنشاء theme جديد
        new_theme = Theme(
            name=theme_data.get("name"),
            primary_color=theme_data.get("primary_color", "#FF6B6B"),
            secondary_color=theme_data.get("secondary_color", "#4ECDC4"),
            font=theme_data.get("font", "Arial"),
            icon_set=theme_data.get("icon_set", "default")
        )

        db.add(new_theme)
        db.commit()
        db.refresh(new_theme)

        return {
            "message": "تم إنشاء الـtheme بنجاح",
            "theme": {
                "id": new_theme.id,
                "name": new_theme.name,
                "primary_color": new_theme.primary_color,
                "secondary_color": new_theme.secondary_color,
                "font": new_theme.font,
                "icon_set": new_theme.icon_set,
                "created_at": new_theme.created_at.isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/themes/apply")
async def apply_theme(theme_data: dict, db: Session = Depends(get_db)):
    """تطبيق theme"""
    try:
        # البحث عن الـtheme الحالي أو إنشاؤه
        current_theme = db.query(Theme).filter(Theme.name == "current").first()
        if not current_theme:
            current_theme = Theme(name="current")
            db.add(current_theme)

        # تحديث الـtheme الحالي
        current_theme.primary_color = theme_data.get("primary_color", "#FF6B6B")
        current_theme.secondary_color = theme_data.get("secondary_color", "#4ECDC4")
        current_theme.font = theme_data.get("font", "Arial")
        current_theme.icon_set = theme_data.get("icon_set", "default")

        db.commit()

        return {
            "message": "تم تطبيق الـtheme بنجاح",
            "theme": {
                "primary_color": current_theme.primary_color,
                "secondary_color": current_theme.secondary_color,
                "font": current_theme.font,
                "icon_set": current_theme.icon_set
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/themes/{theme_id}")
async def delete_theme(theme_id: int, db: Session = Depends(get_db)):
    """حذف theme"""
    try:
        theme = db.query(Theme).filter(Theme.id == theme_id).first()
        if not theme:
            raise HTTPException(status_code=404, detail="الـtheme غير موجود")

        # منع حذف الـtheme الحالي
        if theme.name == "current":
            raise HTTPException(status_code=400, detail="لا يمكن حذف الـtheme الحالي")

        db.delete(theme)
        db.commit()

        return {"message": "تم حذف الـtheme بنجاح"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ==================== تصدير البيانات ====================

@router.get("/export/sessions")
async def export_sessions(
    format: str = "csv",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """تصدير بيانات الجلسات"""
    try:
        query = db.query(DBSession)

        if start_date:
            query = query.filter(DBSession.created_at >= datetime.fromisoformat(start_date))
        if end_date:
            query = query.filter(DBSession.created_at <= datetime.fromisoformat(end_date))

        sessions = query.all()

        data = []
        for session in sessions:
            participants_count = db.query(Participant).filter(Participant.session_id == session.id).count()
            winners_count = db.query(Winner).filter(Winner.session_id == session.id).count()

            data.append({
                "id": session.id,
                "name": session.name,
                "game_type": session.game_type,
                "status": session.status,
                "created_at": session.created_at.isoformat(),
                "closed_at": session.closed_at.isoformat() if session.closed_at else None,
                "participants_count": participants_count,
                "winners_count": winners_count
            })

        if format.lower() == "csv":
            df = pd.DataFrame(data)
            csv_content = df.to_csv(index=False)
            return {"content": csv_content, "filename": f"sessions_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"}
        elif format.lower() == "json":
            return {"content": json.dumps(data, ensure_ascii=False, indent=2), "filename": f"sessions_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"}
        else:
            raise HTTPException(status_code=400, detail="التنسيق غير مدعوم")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/export/winners")
async def export_winners(
    format: str = "csv",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """تصدير بيانات الفائزين"""
    try:
        query = db.query(Winner)

        if start_date:
            query = query.filter(Winner.win_time >= datetime.fromisoformat(start_date))
        if end_date:
            query = query.filter(Winner.win_time <= datetime.fromisoformat(end_date))

        winners = query.all()

        data = []
        for winner in winners:
            session = db.query(DBSession).filter(DBSession.id == winner.session_id).first()
            data.append({
                "id": winner.id,
                "user_id": winner.user_id,
                "username": winner.username,
                "win_time": winner.win_time.isoformat(),
                "session_name": session.name if session else "غير معروف",
                "game_type": session.game_type if session else "غير معروف"
            })

        if format.lower() == "csv":
            df = pd.DataFrame(data)
            csv_content = df.to_csv(index=False)
            return {"content": csv_content, "filename": f"winners_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"}
        elif format.lower() == "json":
            return {"content": json.dumps(data, ensure_ascii=False, indent=2), "filename": f"winners_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"}
        else:
            raise HTTPException(status_code=400, detail="التنسيق غير مدعوم")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ==================== سجلات الأدمن ====================

@router.get("/admin/logs")
async def get_admin_logs(
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """الحصول على سجلات الأدمن"""
    try:
        logs = db.query(AdminLog).order_by(AdminLog.timestamp.desc()).offset(offset).limit(limit).all()

        return {
            "logs": [
                {
                    "id": log.id,
                    "action": log.action,
                    "performed_by": log.performed_by,
                    "details": log.details,
                    "timestamp": log.timestamp.isoformat()
                } for log in logs
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))