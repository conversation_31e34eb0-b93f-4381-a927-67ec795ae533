import React, { useState } from 'react';
import { Bell, User, LogOut, Settings } from 'lucide-react';

const Header = ({ user, onLogout }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  return (
    <header className="bg-white shadow-sm border-bottom px-4 py-3">
      <div className="d-flex align-items-center justify-content-between">
        {/* Left side */}
        <div className="d-flex align-items-center">
          <h2 className="h4 fw-bold text-dark mb-0">لوحة التحكم</h2>
        </div>

        {/* Right side */}
        <div className="d-flex align-items-center">
          {/* Notifications */}
          <button className="btn btn-link text-muted position-relative me-3 p-2">
            <Bell size={20} />
            <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style={{fontSize: '8px', padding: '2px 4px'}}>
              <span className="visually-hidden">إشعارات جديدة</span>
            </span>
          </button>

          {/* User Menu */}
          <div className="dropdown">
            <button
              className="btn btn-link text-decoration-none d-flex align-items-center p-2"
              type="button"
              data-bs-toggle="dropdown"
              aria-expanded="false"
              onClick={() => setShowDropdown(!showDropdown)}
            >
              <div className="bg-gradient text-white rounded-circle d-flex align-items-center justify-content-center me-2" style={{width: '32px', height: '32px'}}>
                <User size={16} />
              </div>
              <div className="text-end">
                <p className="small fw-medium text-dark mb-0">
                  {user?.username || 'المدير'}
                </p>
                <p className="small text-muted mb-0">مدير النظام</p>
              </div>
            </button>

            {/* Dropdown Menu */}
            <ul className={`dropdown-menu dropdown-menu-end ${showDropdown ? 'show' : ''}`}>
              <li>
                <button className="dropdown-item d-flex align-items-center">
                  <Settings size={16} className="me-2" />
                  <span>الإعدادات</span>
                </button>
              </li>
              <li><hr className="dropdown-divider" /></li>
              <li>
                <button
                  onClick={onLogout}
                  className="dropdown-item d-flex align-items-center text-danger"
                >
                  <LogOut size={16} className="me-2" />
                  <span>تسجيل الخروج</span>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Close dropdown when clicking outside */}
      {showDropdown && (
        <div
          className="position-fixed top-0 start-0 w-100 h-100"
          style={{zIndex: 1040}}
          onClick={() => setShowDropdown(false)}
        />
      )}
    </header>
  );
};

export default Header;