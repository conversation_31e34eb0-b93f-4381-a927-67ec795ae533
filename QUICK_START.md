# 🚀 دليل البدء السريع - نظام إدارة الكازينو

## 📋 المتطلبات الأساسية

- Python 3.8+
- Node.js 16+
- npm أو yarn

## ⚡ البدء السريع

### 1. تشغيل الخادم الخلفي (Backend)

#### الطريقة الأولى: استخدام ملف الباتش
```bash
# في Windows
start-backend.bat

# أو في Linux/Mac
./start-backend.sh
```

#### الطريقة الثانية: تشغيل مباشر
```bash
# من المجلد الرئيسي
python main.py
```

### 2. تشغيل واجهة المستخدم (Frontend)

#### الطريقة الأولى: استخدام ملف الباتش
```bash
# في Windows
start-frontend.bat

# أو في Linux/Mac
./start-frontend.sh
```

#### الطريقة الثانية: تشغيل مباشر
```bash
# من مجلد الواجهة
cd frontend/dashboard
npm start
```

## 🌐 الوصول للتطبيق

- **لوحة التحكم**: http://localhost:3000
- **API الخادم**: http://localhost:8000
- **وثائق API**: http://localhost:8000/docs

## ⚙️ إعداد Discord Bot (اختياري)

إذا كنت تريد استخدام بوت Discord:

1. انسخ ملف `.env.example` إلى `.env`
2. املأ معلومات Discord Bot:
   ```
   DISCORD_TOKEN=your_bot_token_here
   DISCORD_CLIENT_ID=your_client_id_here
   DISCORD_GUILD_ID=your_guild_id_here
   ```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في تشغيل Backend**:
   - تأكد من تثبيت Python dependencies: `pip install -r requirements.txt`
   - تأكد من وجود ملف `.env`

2. **خطأ في تشغيل Frontend**:
   - تأكد من أنك في المجلد الصحيح: `frontend/dashboard`
   - تأكد من تثبيت Node.js dependencies: `npm install`

3. **خطأ Discord Bot**:
   - إذا لم تكن تريد استخدام Discord Bot، اترك `DISCORD_TOKEN` فارغاً
   - النظام سيعمل بدون Discord Bot

## 📁 هيكل المشروع

```
ISlotcall/
├── main.py                 # الملف الرئيسي للخادم
├── backend/               # كود الخادم الخلفي
├── frontend/              # واجهات المستخدم
│   ├── dashboard/         # لوحة التحكم
│   └── overlay/           # شاشة العرض
├── database/              # قاعدة البيانات
├── start-backend.bat      # تشغيل الخادم (Windows)
├── start-frontend.bat     # تشغيل الواجهة (Windows)
└── .env                   # متغيرات البيئة
```

## 🎯 الميزات المتاحة

### بدون Discord Bot:
- ✅ لوحة تحكم كاملة
- ✅ إدارة الجلسات
- ✅ إدارة المستخدمين
- ✅ إدارة السمات
- ✅ الإعدادات

### مع Discord Bot:
- ✅ جميع الميزات السابقة
- ✅ أوامر Discord
- ✅ إدارة الجلسات عبر Discord
- ✅ اختيار الفائزين
- ✅ إحصائيات المستخدمين

## 📞 الدعم

للمساعدة التقنية، راجع:
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/)
- [Discord.py Documentation](https://discordpy.readthedocs.io/) 