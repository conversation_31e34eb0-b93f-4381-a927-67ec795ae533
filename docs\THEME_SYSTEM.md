# 🎨 نظام إدارة السمات (Theme System)

## نظرة عامة

نظام إدارة السمات المتطور يوفر تحكماً كاملاً في مظهر الموقع والـ Overlay، مع دعم للألوان المخصصة، الخطوط، ومجموعات الأيقونات المختلفة.

## ✨ المميزات الرئيسية

### 🎯 إدارة السمات
- **إنشاء سمات مخصصة** مع ألوان وخطوط مختلفة
- **معاينة فورية** للسمات قبل التطبيق
- **حفظ وتحميل** السمات المفضلة
- **تصدير واستيراد** السمات بصيغة JSON
- **سمات افتراضية** جاهزة للاستخدام

### 🎨 خيارات التخصيص
- **الألوان**: لون أساسي وثانوي مع معاينة فورية
- **الخطوط**: Cairo, Roboto, Poppins, Inter
- **مجموعات الأيقونات**: افتراضي, عصري, ألعاب, نيون
- **تأثيرات بصرية** متقدمة حسب نوع الأيقونات

### 🔄 التحديث المباشر
- **Socket.IO Integration** للتحديث الفوري
- **CSS Variables** للتطبيق السلس
- **Theme Context** لإدارة الحالة عبر التطبيق

## 🏗️ البنية التقنية

### قاعدة البيانات
```sql
CREATE TABLE themes (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    primary_color VARCHAR(7) NOT NULL,
    secondary_color VARCHAR(7),
    font VARCHAR(100) DEFAULT 'Cairo',
    icon_set VARCHAR(100) DEFAULT 'default',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### API Endpoints
- `GET /api/themes` - جلب جميع السمات
- `GET /api/themes/current` - جلب السمة الحالية
- `POST /api/themes` - إنشاء سمة جديدة
- `POST /api/themes/apply` - تطبيق سمة
- `DELETE /api/themes/{id}` - حذف سمة

### Frontend Components
- `pages/Themes.js` - صفحة إدارة السمات الرئيسية
- `components/ThemePreview.js` - مكونات معاينة السمات
- `contexts/ThemeContext.js` - إدارة حالة السمات
- `services/themeService.js` - خدمات API للسمات

## 🎮 استخدام النظام

### 1. إنشاء سمة جديدة
1. اذهب إلى صفحة "إدارة السمات"
2. اضغط على "سمة جديدة"
3. اختر الألوان والخط ومجموعة الأيقونات
4. اضغط "حفظ السمة"

### 2. تطبيق سمة موجودة
1. اختر السمة من الشبكة
2. اضغط "تطبيق" أو انقر على السمة مباشرة
3. ستظهر التغييرات فوراً في الـ Dashboard والـ Overlay

### 3. تصدير واستيراد السمات
- **التصدير**: اضغط على أيقونة التحميل لحفظ ملف JSON
- **الاستيراد**: اضغط على أيقونة الرفع واختر ملف JSON

### 4. السمات الافتراضية
اضغط على "سمات افتراضية" لإنشاء مجموعة من السمات الجاهزة:
- **الليل الداكن**: ألوان داكنة للاستخدام الليلي
- **الغروب الذهبي**: ألوان دافئة برتقالية
- **البحر الأزرق**: ألوان زرقاء منعشة
- **الغابة الخضراء**: ألوان خضراء طبيعية
- **النيون الوردي**: ألوان متوهجة للألعاب

## 🎨 مجموعات الأيقونات

### افتراضي (Default)
- أيقونات كلاسيكية بسيطة
- مناسبة للاستخدام العام

### عصري (Modern)
- أيقونات حديثة مع ظلال خفيفة
- تصميم مبسط وأنيق

### ألعاب (Gaming)
- أيقونات مع تأثيرات متوهجة
- مناسبة لبيئة الألعاب

### نيون (Neon)
- أيقونات متوهجة مع حركة
- تأثيرات بصرية متقدمة

## 🔧 التخصيص المتقدم

### CSS Variables المتاحة
```css
:root {
  --primary-color: #FF6B6B;
  --secondary-color: #4ECDC4;
  --accent-color: #45B7D1;
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --bg-overlay: rgba(0, 0, 0, 0.8);
  --bg-card: rgba(255, 255, 255, 0.1);
}
```

### Theme-aware Classes
- `.theme-primary` - خلفية باللون الأساسي
- `.theme-secondary` - خلفية باللون الثانوي
- `.theme-gradient` - خلفية متدرجة
- `.theme-primary-text` - نص باللون الأساسي
- `.theme-glow` - تأثير توهج
- `.theme-border` - حدود باللون الأساسي

### استخدام Theme Context
```javascript
import { useTheme } from '../contexts/ThemeContext';

const MyComponent = () => {
  const { 
    currentTheme, 
    applyTheme, 
    getThemeColor 
  } = useTheme();

  return (
    <div style={{ color: getThemeColor('primary') }}>
      المحتوى
    </div>
  );
};
```

## 🚀 التطوير والتوسع

### إضافة خطوط جديدة
1. أضف الخط إلى `index.css`:
```css
@import url('https://fonts.googleapis.com/css2?family=NewFont:wght@300;400;500;600;700&display=swap');
```

2. أضف الخط إلى `fontOptions` في `Themes.js`

### إضافة مجموعة أيقونات جديدة
1. أضف الخيار إلى `iconSetOptions` في `Themes.js`
2. أضف CSS classes في `index.css`:
```css
.icon-set-newset .theme-icon {
  /* تأثيرات مخصصة */
}
```

### إضافة خصائص جديدة للسمة
1. أضف العمود إلى جدول `themes` في قاعدة البيانات
2. حدث `Theme` model في `database.py`
3. حدث API endpoints في `routes.py`
4. حدث Frontend components

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

**السمة لا تطبق على الـ Overlay:**
- تأكد من تشغيل الباك إند
- تحقق من اتصال Socket.IO
- تأكد من صحة CSS Variables

**الخطوط لا تظهر:**
- تأكد من تحميل الخط في `index.css`
- تحقق من صحة اسم الخط
- تأكد من اتصال الإنترنت لـ Google Fonts

**الألوان لا تتغير:**
- تحقق من صحة قيم الألوان (hex format)
- تأكد من تطبيق CSS Variables
- أعد تحميل الصفحة

## 📱 التوافق

- ✅ جميع المتصفحات الحديثة
- ✅ الأجهزة المحمولة
- ✅ OBS Studio للـ Overlay
- ✅ دعم RTL للغة العربية

## 🔮 التطويرات المستقبلية

- [ ] محرر ألوان متقدم
- [ ] سمات ديناميكية حسب الوقت
- [ ] تأثيرات حركية مخصصة
- [ ] دعم الصور المخصصة
- [ ] سمات مجتمعية قابلة للمشاركة
- [ ] معاينة ثلاثية الأبعاد
- [ ] دعم الوضع الداكن/الفاتح
- [ ] سمات تفاعلية مع الألعاب
