import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  Gamepad2,
  Trophy,
  Users,
  Palette,
  Settings,
  BarChart3
} from 'lucide-react';

const Sidebar = () => {
  const location = useLocation();

  const menuItems = [
    {
      path: '/dashboard',
      name: 'الرئيسية',
      icon: Home
    },
    {
      path: '/sessions',
      name: 'الجلسات',
      icon: Gamepad2
    },
    {
      path: '/winners',
      name: 'الفائزين',
      icon: Trophy
    },
    {
      path: '/users',
      name: 'المستخدمين',
      icon: Users
    },
    {
      path: '/themes',
      name: 'إدارة السمات',
      icon: Palette
    },
    {
      path: '/settings',
      name: 'الإعدادات',
      icon: Settings
    }
  ];

  return (
    <div className="bg-white shadow-lg vh-100 position-fixed" style={{width: '256px'}}>
      {/* Logo */}
      <div className="p-4 border-bottom">
        <div className="d-flex align-items-center">
          <div className="bg-gradient text-white rounded d-flex align-items-center justify-content-center me-3" style={{width: '40px', height: '40px'}}>
            <BarChart3 size={24} />
          </div>
          <div>
            <h1 className="h5 fw-bold text-dark mb-0">ISlotCall</h1>
            <p className="small text-muted mb-0">لوحة التحكم</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-3">
        <ul className="list-unstyled mb-0">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;

            return (
              <li key={item.path} className="mb-1">
                <Link
                  to={item.path}
                  className={`d-flex align-items-center text-decoration-none p-3 rounded transition-all ${
                    isActive
                      ? 'bg-primary text-white shadow-sm'
                      : 'text-muted'
                  }`}
                  style={{
                    ':hover': !isActive ? { backgroundColor: '#f8f9fa', color: '#495057' } : {}
                  }}
                >
                  <Icon size={20} className="me-3" />
                  <span className="fw-medium">{item.name}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="position-absolute bottom-0 start-0 end-0 p-3 border-top bg-light">
        <div className="text-center">
          <p className="small text-muted mb-0">نظام إدارة جلسات الكازينو</p>
          <p className="small text-muted mb-0">الإصدار 1.0.0</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;